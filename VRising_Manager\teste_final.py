#!/usr/bin/env python3
"""
Teste final para verificar todas as funcionalidades do V Rising Manager
"""

import os
import re

def test_all_features():
    print("🧪 TESTE FINAL - V Rising Manager")
    print("=" * 60)
    
    # 1. Verificar estrutura de arquivos
    print("\n📁 1. VERIFICANDO ESTRUTURA DE ARQUIVOS:")
    files_to_check = [
        ("../VRisingServer.exe", "Executável do servidor"),
        ("../save-data/Settings/adminlist.txt", "Lista de administradores"),
        ("../save-data/Settings/banlist.txt", "Lista de banidos"),
        ("../save-data/Saves/v4/crepusculo", "Save do jogo"),
        ("../logs", "Pasta de logs"),
        ("vrising_manager.py", "Gerenciador principal")
    ]
    
    for file_path, description in files_to_check:
        exists = os.path.exists(file_path)
        status = "✅" if exists else "❌"
        print(f"  {status} {description}: {file_path}")
    
    # 2. Verificar conteúdo do adminlist
    print("\n👑 2. VERIFICANDO ADMINISTRADORES:")
    admin_file = "../save-data/Settings/adminlist.txt"
    if os.path.exists(admin_file):
        with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            admins = [line.strip() for line in lines if line.strip()]
        
        print(f"  📊 Total de admins: {len(admins)}")
        for i, admin in enumerate(admins, 1):
            print(f"  {i}. {admin}")
            
        # Verificar se o Steam ID específico está na lista
        target_id = "76561198832506980"
        if target_id in admins:
            print(f"  ✅ Steam ID {target_id} está configurado como admin")
        else:
            print(f"  ❌ Steam ID {target_id} NÃO está na lista de admins")
    else:
        print("  ❌ Arquivo adminlist.txt não encontrado")
    
    # 3. Verificar logs de jogadores
    print("\n🎮 3. VERIFICANDO LOGS DE JOGADORES:")
    logs_path = "../logs"
    if os.path.exists(logs_path):
        log_files = [f for f in os.listdir(logs_path) if f.endswith('.log')]
        if log_files:
            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(logs_path, f)))
            log_path = os.path.join(logs_path, latest_log)
            
            print(f"  📄 Log mais recente: {latest_log}")
            
            # Procurar por conexões de jogadores
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            player_connections = []
            for line in lines[-100:]:  # Últimas 100 linhas
                if 'connected' in line and re.search(r'\d{17}', line):
                    player_connections.append(line.strip())
            
            print(f"  📊 Conexões encontradas: {len(player_connections)}")
            
            if player_connections:
                print("  🔍 Últimas conexões:")
                for conn in player_connections[-3:]:  # Últimas 3
                    # Extrair informações
                    steam_match = re.search(r'(\d{17})', conn)
                    char_match = re.search(r"Character:\s*'([^']+)'", conn)
                    time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', conn)
                    
                    steam_id = steam_match.group(1) if steam_match else "N/A"
                    character = char_match.group(1) if char_match else "N/A"
                    time = time_match.group(1) if time_match else "N/A"
                    
                    print(f"    🎮 {character} ({steam_id}) às {time}")
            else:
                print("  ⚠️ Nenhuma conexão de jogador encontrada nos logs recentes")
        else:
            print("  ❌ Nenhum arquivo de log encontrado")
    else:
        print("  ❌ Pasta de logs não encontrada")
    
    # 4. Testar padrões de detecção
    print("\n🔍 4. TESTANDO PADRÕES DE DETECÇÃO:")
    test_line = "[09:16:16] User '{Lidgren 192.168.1.100:53283}' '76561198832506980', approvedUserIndex: 0, Character: 'Renee' connected as ID '0,1', Entity '323167,1'."
    
    patterns = [
        r"User.*'(\d{17})'.*Character:\s*'([^']+)'.*connected.*Entity\s*'(\d+,\d+)'",
        r"User.*'(\d{17})'.*connected",
        r"(\d{17}).*Character:\s*'([^']+)'.*connected",
        r".*'(\d{17})'.*connected"
    ]
    
    print(f"  📝 Linha de teste: {test_line[:50]}...")
    
    for i, pattern in enumerate(patterns, 1):
        match = re.search(pattern, test_line, re.IGNORECASE)
        if match:
            print(f"  ✅ Padrão {i}: Detectou Steam ID {match.group(1)}")
            if len(match.groups()) > 1:
                print(f"      Nome: {match.group(2)}")
        else:
            print(f"  ❌ Padrão {i}: Não funcionou")
    
    # 5. Verificar dependências Python
    print("\n🐍 5. VERIFICANDO DEPENDÊNCIAS PYTHON:")
    dependencies = ['customtkinter', 'psutil']
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"  ✅ {dep}: Instalado")
        except ImportError:
            print(f"  ❌ {dep}: NÃO instalado")
    
    # 6. Resumo final
    print("\n📋 6. RESUMO FINAL:")
    print("  ✅ Funcionalidades implementadas:")
    print("    • Detecção de jogadores online")
    print("    • Gerenciamento de administradores")
    print("    • Sistema de banimento")
    print("    • Interface com abas melhorada")
    print("    • Monitoramento automático")
    print("    • Ferramentas de debug")
    
    print("\n🚀 PRÓXIMOS PASSOS:")
    print("  1. Execute: python vrising_manager.py")
    print("  2. Vá para aba '👥 Jogadores'")
    print("  3. Verifique aba '👑 Admins' - deve mostrar seu Steam ID")
    print("  4. Conecte no servidor para testar detecção")
    print("  5. Use botão '🔍 Debug Logs' se houver problemas")
    
    print("\n" + "=" * 60)
    print("🎯 TESTE CONCLUÍDO!")

if __name__ == "__main__":
    test_all_features()
    input("\nPressione Enter para sair...")
