@echo off
chcp 65001 >nul
echo ========================================
echo V Rising Server Manager - Instalação
echo ========================================
echo.

REM Verificar se Python está instalado
echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ ERRO: Python não encontrado!
    echo.
    echo 📥 Para instalar Python:
    echo 1. Acesse: https://python.org/downloads/
    echo 2. Baixe Python 3.8 ou superior
    echo 3. Durante a instalação, marque "Add Python to PATH"
    echo 4. Execute este script novamente
    echo.
    echo 💡 Alternativa: Instale pelo Microsoft Store
    echo    Digite "python" no menu iniciar e clique em "Obter"
    echo.
    pause
    exit /b 1
)

echo ✅ Python encontrado!
python --version
echo.

REM Verificar se pip está disponível
echo Verificando pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: pip não encontrado!
    echo Reinstale Python com pip incluído.
    pause
    exit /b 1
)

echo ✅ pip encontrado!
echo.

REM Instalar dependências
echo 📦 Instalando dependências...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo ⚠️ Falha na instalação padrão. Tentando com --user...
    pip install --user -r requirements.txt

    if errorlevel 1 (
        echo.
        echo ❌ ERRO: Não foi possível instalar as dependências!
        echo.
        echo 🔧 Tente manualmente:
        echo pip install customtkinter psutil
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo ✅ Instalação concluída com sucesso!
echo ========================================
echo.
echo 🚀 Para executar o gerenciador:
echo    python vrising_manager.py
echo.
echo 📖 Leia o README_MANAGER.md para mais informações
echo.
pause
