{"Keys": ["com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.vivox.version", "com.unity.services.vivox.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names", "com.unity.services.vivox.server", "com.unity.services.vivox.domain", "com.unity.services.vivox.issuer", "com.unity.services.vivox.is-environment-custom", "com.unity.services.vivox.is-test-mode", "com.unity.services.core.environment-name"], "Values": [{"m_Value": "1.14.0", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null;Unity.Services.Core.Internal.IInitializablePackageV2, Unity.Services.Core.Internal, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "15.1.210000-pre.1", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Vivox.VivoxPackageInitializer, Unity.Services.Vivox, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.services.core;com.unity.services.vivox", "m_IsReadOnly": false}, {"m_Value": "https://unity.vivox.com/appconfig/79718-vrisi-18272-test", "m_IsReadOnly": false}, {"m_Value": "mtu1xp.vivox.com", "m_IsReadOnly": false}, {"m_Value": "79718-vrisi-18272-test", "m_IsReadOnly": false}, {"m_Value": "False", "m_IsReadOnly": false}, {"m_Value": "False", "m_IsReadOnly": false}, {"m_Value": "production", "m_IsReadOnly": false}]}