Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Plugins: Failed to load 'C:/server/JackSparrow/VRisingServer_Data/Plugins\x86_64/BacktraceCrashpadWindows.dll' because one or more of its dependencies could not be loaded.
Initialize engine version: 2022.3.58f1 (ed7f6eacb62e)
[Subsystems] Discovering subsystems at path C:/server/JackSparrow/VRisingServer_Data/UnitySubsystems
Forcing GfxDevice: Null
GfxDevice: creating device client; threaded=0; jobified=0
NullGfxDevice:
    Version:  NULL 1.0 [1.0]
    Renderer: Null Device
    Vendor:   Unity Technologies
There is no texture data available to upload.
Microsoft Media Foundation video decoding to texture disabled: graphics device is Null, only Direct3D 11 and Direct3D 12 (only on desktop) are supported for hardware-accelerated video decoding.
There is no texture data available to upload.
There is no texture data available to upload.
There is no texture data available to upload.
There is no texture data available to upload.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
UnloadTime: 0.646200 ms
AutomaticWorldBootstrap.Initialize
Set a new SaveFileManager 'Stunlock.Platform.PC.SaveFileManagerPC'
Loaded VersionDataSettings:
{
  "NetworkVersion": 24,
  "PersistenceVersion": 4
}
Loaded GeneralGameSettings:
{
  "JobWorkerCount": 4,
  "EnableDebugging": false,
  "SteamLogLevel": 5,
  "SteamCaptureAPILogs": false,
  "SteamCaptureNetworkingLogs": false
}
PersistentDataPath: save-data root: save-data\Saves
PersistenceVersionOverride value found from VersionDataSettings: 4
Persistence Version initialized as: 4
ProjectM.ServerHostSettings - 'Name' ('System.String') overridden by Command Line Parameter '-serverName' - New value: VRising Server
ProjectM.ServerHostSettings - 'SaveName' ('System.String') overridden by Command Line Parameter '-saveName' - New value: crepusculo
Loaded ServerHostSettings:
{
  "Name": "VRising Server",
  "Description": "",
  "Port": 9876,
  "FallbackPort": 9878,
  "QueryPort": 9877,
  "Address": null,
  "MaxConnectedUsers": 40,
  "MaxConnectedAdmins": 4,
  "MinFreeSlotsNeededForNewUsers": 0,
  "ServerFps": 30,
  "AIUpdatesPerFrame": 200,
  "Password": "",
  "Secure": true,
  "Authenticate": true,
  "ListOnSteam": false,
  "ServerBranch": "",
  "GameSettingsPreset": "",
  "GameDifficultyPreset": "",
  "SaveName": "crepusculo",
  "SaveFileName": "",
  "AutoSaveCount": 10,
  "AutoSaveInterval": 120,
  "CompressSaveFiles": true,
  "AutoSaveSmartKeep": "10:1:1,30:0:1,60:0:1,120:0:1,180:0:1,240:0:1,360:0:1,720:0:1,1440:0:1,52560000:99:0",
  "RunPersistenceTestsOnSave": false,
  "DumpPersistenceSummaryOnSave": false,
  "StorePersistenceDebugData": false,
  "GiveStarterItems": false,
  "LogAllNetworkEvents": false,
  "LogAdminEvents": true,
  "LogDebugEvents": true,
  "AdminOnlyDebugEvents": true,
  "EveryoneIsAdmin": false,
  "DisableDebugEvents": false,
  "EnableDangerousDebugEvents": false,
  "TrackArchetypeCreationsOnStartup": false,
  "ServerStartTimeOffset": 0.0,
  "PersistenceVersionOverride": -1,
  "LanMode": false,
  "LowerFPSWhenEmpty": false,
  "LowerFPSWhenEmptyValue": 1,
  "UseTeleportPlayersOutOfCollisionFix": true,
  "RemoteBansURL": "",
  "RemoteAdminsURL": "",
  "ResetDaysInterval": 0,
  "DayOfReset": -1,
  "AFKKickType": 0,
  "AFKKickDuration": 1,
  "AFKKickWarningDuration": 14,
  "AFKKickPlayerRatio": 0.5,
  "ListOnEOS": false,
  "EosLogLevel": -1,
  "HideIPAddress": false,
  "EnableBacktraceANR": false,
  "AnalyticsEnabled": true,
  "AnalyticsEnvironment": "prod",
  "AnalyticsDebug": false,
  "UseDoubleTransportLayer": true,
  "PrivateGame": false,
  "SafeReconnectTime": 300.0,
  "SafeReconnectSlots": 10,
  "DisableAfterReset": false,
  "UTCOffset": -2147483648,
  "API": {
    "Enabled": false,
    "BindAddress": "*",
    "BindPort": 9090,
    "BasePath": "/",
    "AccessList": "",
    "PrometheusDelay": 30
  },
  "Rcon": {
    "Enabled": false,
    "BindAddress": null,
    "Port": 25575,
    "Password": "",
    "TimeoutSeconds": 300,
    "MaxPasswordTries": 99,
    "BanMinutes": 0,
    "SendAuthImmediately": true,
    "MaxConnectionsPerIp": 20,
    "MaxConnections": 20,
    "ExperimentalCommandsEnabled": false
  }
}
Loaded ServerGameSettings:
{
  "GameDifficulty": 1,
  "GameModeType": 1,
  "CastleDamageMode": 0,
  "SiegeWeaponHealth": 2,
  "PlayerDamageMode": 0,
  "CastleHeartDamageMode": 1,
  "PvPProtectionMode": 3,
  "DeathContainerPermission": 0,
  "RelicSpawnType": 0,
  "CanLootEnemyContainers": true,
  "BloodBoundEquipment": true,
  "TeleportBoundItems": true,
  "BatBoundItems": false,
  "BatBoundShards": false,
  "AllowGlobalChat": true,
  "AllWaypointsUnlocked": false,
  "FreeCastleRaid": false,
  "FreeCastleClaim": false,
  "FreeCastleDestroy": false,
  "InactivityKillEnabled": true,
  "InactivityKillTimeMin": 3600,
  "InactivityKillTimeMax": 604800,
  "InactivityKillSafeTimeAddition": 172800,
  "InactivityKillTimerMaxItemLevel": 84,
  "StartingProgressionLevel": 0,
  "WeaponSlots": 8,
  "DisableDisconnectedDeadEnabled": true,
  "DisableDisconnectedDeadTimer": 60,
  "DisconnectedSunImmunityTime": 300.0,
  "InventoryStacksModifier": 1.0,
  "DropTableModifier_General": 1.0,
  "DropTableModifier_Missions": 1.0,
  "DropTableModifier_StygianShards": 1.0,
  "SoulShard_DurabilityLossRate": 1.0,
  "MaterialYieldModifier_Global": 1.0,
  "BloodEssenceYieldModifier": 1.0,
  "JournalVBloodSourceUnitMaxDistance": 25.0,
  "PvPVampireRespawnModifier": 1.0,
  "CastleMinimumDistanceInFloors": 2,
  "ClanSize": 4,
  "BloodDrainModifier": 1.0,
  "DurabilityDrainModifier": 1.0,
  "GarlicAreaStrengthModifier": 1.0,
  "HolyAreaStrengthModifier": 1.0,
  "SilverStrengthModifier": 1.0,
  "SunDamageModifier": 1.0,
  "CastleDecayRateModifier": 1.0,
  "CastleBloodEssenceDrainModifier": 1.0,
  "CastleSiegeTimer": 420.0,
  "CastleUnderAttackTimer": 60.0,
  "CastleRaidTimer": 600.0,
  "CastleRaidProtectionTime": 1800.0,
  "CastleExposedFreeClaimTimer": 300.0,
  "CastleRelocationCooldown": 10800.0,
  "CastleRelocationEnabled": true,
  "AnnounceSiegeWeaponSpawn": true,
  "ShowSiegeWeaponMapIcon": false,
  "BuildCostModifier": 1.0,
  "RecipeCostModifier": 1.0,
  "CraftRateModifier": 1.0,
  "ResearchCostModifier": 1.0,
  "RefinementCostModifier": 1.0,
  "RefinementRateModifier": 1.0,
  "ResearchTimeModifier": 1.0,
  "DismantleResourceModifier": 1.0,
  "ServantConvertRateModifier": 1.0,
  "RepairCostModifier": 1.0,
  "Death_DurabilityFactorLoss": 0.125,
  "Death_DurabilityLossFactorAsResources": 1.0,
  "StarterEquipmentId": 0,
  "StarterResourcesId": 0,
  "VBloodUnitSettings": [],
  "UnlockedAchievements": [],
  "UnlockedResearchs": [],
  "GameTimeModifiers": {
    "DayDurationInSeconds": 1080.0,
    "DayStartHour": 9,
    "DayStartMinute": 0,
    "DayEndHour": 17,
    "DayEndMinute": 0,
    "BloodMoonFrequency_Min": 10,
    "BloodMoonFrequency_Max": 18,
    "BloodMoonBuff": 0.2
  },
  "VampireStatModifiers": {
    "MaxHealthModifier": 1.0,
    "PhysicalPowerModifier": 1.0,
    "SpellPowerModifier": 1.0,
    "ResourcePowerModifier": 1.0,
    "SiegePowerModifier": 1.0,
    "DamageReceivedModifier": 1.0,
    "ReviveCancelDelay": 5.0
  },
  "UnitStatModifiers_Global": {
    "MaxHealthModifier": 1.0,
    "PowerModifier": 1.0,
    "LevelIncrease": 0
  },
  "UnitStatModifiers_VBlood": {
    "MaxHealthModifier": 1.0,
    "PowerModifier": 1.0,
    "LevelIncrease": 0
  },
  "EquipmentStatModifiers_Global": {
    "MaxHealthModifier": 1.0,
    "ResourceYieldModifier": 1.0,
    "PhysicalPowerModifier": 1.0,
    "SpellPowerModifier": 1.0,
    "SiegePowerModifier": 1.0,
    "MovementSpeedModifier": 1.0
  },
  "CastleStatModifiers_Global": {
    "TickPeriod": 5.0,
    "SafetyBoxLimit": 1,
    "EyeStructuresLimit": 1,
    "TombLimit": 12,
    "VerminNestLimit": 4,
    "PrisonCellLimit": 24,
    "HeartLimits": {
      "Level1": {
        "FloorLimit": 50,
        "ServantLimit": 1,
        "HeightLimit": 3
      },
      "Level2": {
        "FloorLimit": 140,
        "ServantLimit": 3,
        "HeightLimit": 3
      },
      "Level3": {
        "FloorLimit": 240,
        "ServantLimit": 5,
        "HeightLimit": 3
      },
      "Level4": {
        "FloorLimit": 360,
        "ServantLimit": 7,
        "HeightLimit": 3
      },
      "Level5": {
        "FloorLimit": 550,
        "ServantLimit": 8,
        "HeightLimit": 3
      }
    },
    "CastleHeartLimitType": 0,
    "CastleLimit": 2,
    "NetherGateLimit": 1,
    "ThroneOfDarknessLimit": 1,
    "ArenaStationLimit": 5,
    "RoutingStationLimit": 10
  },
  "PlayerInteractionSettings": {
    "TimeZone": 0,
    "VSPlayerWeekdayTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSPlayerWeekendTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSCastleWeekdayTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSCastleWeekendTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    }
  },
  "TraderModifiers": {
    "StockModifier": 1.0,
    "PriceModifier": 1.0,
    "RestockTimerModifier": 1.0
  },
  "WarEventGameSettings": {
    "Interval": 1,
    "MajorDuration": 1,
    "MinorDuration": 1,
    "WeekdayTime": {
      "StartHour": 0,
      "StartMinute": 0,
      "EndHour": 23,
      "EndMinute": 59
    },
    "WeekendTime": {
      "StartHour": 0,
      "StartMinute": 0,
      "EndHour": 23,
      "EndMinute": 59
    },
    "ScalingPlayers1": {
      "PointsModifier": 1.0,
      "DropModifier": 1.0
    },
    "ScalingPlayers2": {
      "PointsModifier": 0.5,
      "DropModifier": 0.5
    },
    "ScalingPlayers3": {
      "PointsModifier": 0.25,
      "DropModifier": 0.25
    },
    "ScalingPlayers4": {
      "PointsModifier": 0.25,
      "DropModifier": 0.25
    }
  }
}
Bootstrap - Boot Time: 2025-07-01 23:38:43 UTC, Version: VRisingServer v1.1.1.0-r91296-b3 (202504300031) (Build) (Release) (m)
DEDICATED_SERVER defined.
UNITY_ASSERTIONS defined.
UNITY_DOTS_DEBUG defined.
Burst is Enabled
System Information:
processorFrequency: 2112
deviceModel: 82BS (LENOVO)
operatingSystem: Windows 11  (10.0.26100) 64bit
processorType: Intel(R) Core(TM) i5-10210U CPU @ 1.60GHz
processorCount: 8
systemMemorySize: 8025
graphicsDeviceName: Null Device
graphicsDeviceType: Null
graphicsDeviceVendor: Unity Technologies
graphicsDeviceVendorID: 0
graphicsDeviceVersion: NULL 1.0 [1.0]
graphicsMemorySize: 128
graphicsMultiThreaded: False
graphicsShaderLevel: 30
maxTextureSize: 4096
CPU Score: 6336 (WOW)
Bootstrapping World: Default World
PlatformSystemBase - - Entering OnCreate!
PlatformSystemBase -  Setting Product and Modir: V Rising
PlatformSystemBase -  Server App ID: 1604030
PlatformSystemBase - Steam GameServer Initialized!
SteamNetworking - Successfully logged in with the SteamGameServer API. SteamID: 85568397782118122
EOSPlatformSystem - Entering OnCreate!
EOSPlatformSystem - Skipping EOS initialization - EOS features disabled!
Check Host Server - HostServer: False, DedicatedServer: True
BatchMode Host - CommandLine: VRisingServer.exe -persistentDataPath save-data -serverName "VRising Server" -saveName crepusculo -logFile logs\server_2025-07-01_20-38-36.log
PersistentDataPath: save-data root: save-data\Saves
Attempting to load most recent save file for SaveDirectory: C:\server\JackSparrow\save-data\Saves\v4\crepusculo. SaveToLoad: AutoSave_3.save.gz
Auto save 10 interval 120
CreateAndHostServer - SaveDirectory:C:\server\JackSparrow\save-data\Saves\v4\crepusculo, Loaded Save:AutoSave_3.save.gz
[Debug] ServerGameSettingsSystem - OnCreate
[Debug] ServerGameSettingsSystem - OnCreate - Loading ServerGameSettings via ServerRuntimeSettings settings!
RuntimeSettings.SaveDirectoryPath: C:\server\JackSparrow\save-data\Saves\v4\crepusculo
Final ServerGameSettings Values:
{
  "GameDifficulty": 1,
  "GameModeType": 1,
  "CastleDamageMode": 0,
  "SiegeWeaponHealth": 2,
  "PlayerDamageMode": 0,
  "CastleHeartDamageMode": 1,
  "PvPProtectionMode": 3,
  "DeathContainerPermission": 0,
  "RelicSpawnType": 0,
  "CanLootEnemyContainers": true,
  "BloodBoundEquipment": true,
  "TeleportBoundItems": true,
  "BatBoundItems": false,
  "BatBoundShards": false,
  "AllowGlobalChat": true,
  "AllWaypointsUnlocked": false,
  "FreeCastleRaid": false,
  "FreeCastleClaim": false,
  "FreeCastleDestroy": false,
  "InactivityKillEnabled": true,
  "InactivityKillTimeMin": 3600,
  "InactivityKillTimeMax": 604800,
  "InactivityKillSafeTimeAddition": 172800,
  "InactivityKillTimerMaxItemLevel": 84,
  "StartingProgressionLevel": 0,
  "WeaponSlots": 8,
  "DisableDisconnectedDeadEnabled": true,
  "DisableDisconnectedDeadTimer": 60,
  "DisconnectedSunImmunityTime": 300.0,
  "InventoryStacksModifier": 1.0,
  "DropTableModifier_General": 1.0,
  "DropTableModifier_Missions": 1.0,
  "DropTableModifier_StygianShards": 1.0,
  "SoulShard_DurabilityLossRate": 1.0,
  "MaterialYieldModifier_Global": 1.0,
  "BloodEssenceYieldModifier": 1.0,
  "JournalVBloodSourceUnitMaxDistance": 25.0,
  "PvPVampireRespawnModifier": 1.0,
  "CastleMinimumDistanceInFloors": 2,
  "ClanSize": 4,
  "BloodDrainModifier": 1.0,
  "DurabilityDrainModifier": 1.0,
  "GarlicAreaStrengthModifier": 1.0,
  "HolyAreaStrengthModifier": 1.0,
  "SilverStrengthModifier": 1.0,
  "SunDamageModifier": 1.0,
  "CastleDecayRateModifier": 1.0,
  "CastleBloodEssenceDrainModifier": 1.0,
  "CastleSiegeTimer": 420.0,
  "CastleUnderAttackTimer": 60.0,
  "CastleRaidTimer": 600.0,
  "CastleRaidProtectionTime": 1800.0,
  "CastleExposedFreeClaimTimer": 300.0,
  "CastleRelocationCooldown": 10800.0,
  "CastleRelocationEnabled": true,
  "AnnounceSiegeWeaponSpawn": true,
  "ShowSiegeWeaponMapIcon": false,
  "BuildCostModifier": 1.0,
  "RecipeCostModifier": 1.0,
  "CraftRateModifier": 1.0,
  "ResearchCostModifier": 1.0,
  "RefinementCostModifier": 1.0,
  "RefinementRateModifier": 1.0,
  "ResearchTimeModifier": 1.0,
  "DismantleResourceModifier": 1.0,
  "ServantConvertRateModifier": 1.0,
  "RepairCostModifier": 1.0,
  "Death_DurabilityFactorLoss": 0.125,
  "Death_DurabilityLossFactorAsResources": 1.0,
  "StarterEquipmentId": 0,
  "StarterResourcesId": 0,
  "VBloodUnitSettings": [],
  "UnlockedAchievements": [],
  "UnlockedResearchs": [],
  "GameTimeModifiers": {
    "DayDurationInSeconds": 1080.0,
    "DayStartHour": 9,
    "DayStartMinute": 0,
    "DayEndHour": 17,
    "DayEndMinute": 0,
    "BloodMoonFrequency_Min": 10,
    "BloodMoonFrequency_Max": 18,
    "BloodMoonBuff": 0.2
  },
  "VampireStatModifiers": {
    "MaxHealthModifier": 1.0,
    "PhysicalPowerModifier": 1.0,
    "SpellPowerModifier": 1.0,
    "ResourcePowerModifier": 1.0,
    "SiegePowerModifier": 1.0,
    "DamageReceivedModifier": 1.0,
    "ReviveCancelDelay": 5.0
  },
  "UnitStatModifiers_Global": {
    "MaxHealthModifier": 1.0,
    "PowerModifier": 1.0,
    "LevelIncrease": 0
  },
  "UnitStatModifiers_VBlood": {
    "MaxHealthModifier": 1.0,
    "PowerModifier": 1.0,
    "LevelIncrease": 0
  },
  "EquipmentStatModifiers_Global": {
    "MaxHealthModifier": 1.0,
    "ResourceYieldModifier": 1.0,
    "PhysicalPowerModifier": 1.0,
    "SpellPowerModifier": 1.0,
    "SiegePowerModifier": 1.0,
    "MovementSpeedModifier": 1.0
  },
  "CastleStatModifiers_Global": {
    "TickPeriod": 5.0,
    "SafetyBoxLimit": 1,
    "EyeStructuresLimit": 1,
    "TombLimit": 12,
    "VerminNestLimit": 4,
    "PrisonCellLimit": 24,
    "HeartLimits": {
      "Level1": {
        "FloorLimit": 50,
        "ServantLimit": 1,
        "HeightLimit": 3
      },
      "Level2": {
        "FloorLimit": 140,
        "ServantLimit": 3,
        "HeightLimit": 3
      },
      "Level3": {
        "FloorLimit": 240,
        "ServantLimit": 5,
        "HeightLimit": 3
      },
      "Level4": {
        "FloorLimit": 360,
        "ServantLimit": 7,
        "HeightLimit": 3
      },
      "Level5": {
        "FloorLimit": 550,
        "ServantLimit": 8,
        "HeightLimit": 3
      }
    },
    "CastleHeartLimitType": 0,
    "CastleLimit": 2,
    "NetherGateLimit": 1,
    "ThroneOfDarknessLimit": 1,
    "ArenaStationLimit": 5,
    "RoutingStationLimit": 10
  },
  "PlayerInteractionSettings": {
    "TimeZone": 0,
    "VSPlayerWeekdayTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSPlayerWeekendTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSCastleWeekdayTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    },
    "VSCastleWeekendTime": {
      "StartHour": 20,
      "StartMinute": 0,
      "EndHour": 22,
      "EndMinute": 0
    }
  },
  "TraderModifiers": {
    "StockModifier": 1.0,
    "PriceModifier": 1.0,
    "RestockTimerModifier": 1.0
  },
  "WarEventGameSettings": {
    "Interval": 1,
    "MajorDuration": 1,
    "MinorDuration": 1,
    "WeekdayTime": {
      "StartHour": 0,
      "StartMinute": 0,
      "EndHour": 23,
      "EndMinute": 59
    },
    "WeekendTime": {
      "StartHour": 0,
      "StartMinute": 0,
      "EndHour": 23,
      "EndMinute": 59
    },
    "ScalingPlayers1": {
      "PointsModifier": 1.0,
      "DropModifier": 1.0
    },
    "ScalingPlayers2": {
      "PointsModifier": 0.5,
      "DropModifier": 0.5
    },
    "ScalingPlayers3": {
      "PointsModifier": 0.25,
      "DropModifier": 0.25
    },
    "ScalingPlayers4": {
      "PointsModifier": 0.25,
      "DropModifier": 0.25
    }
  }
}
[Server] LoadSceneAsync Request 'WorldAssetSingleton', WaitForSceneLoad: True, SceneEntity: 1692:1
Starting up ServerSteamTransportLayer. GameServer ID: 85568397782118122
Opening SteamIPv4 socket on port: 9876. Socket: 1
Opening SteamSDR socket on virtual port: 0. Socket: 2
[Server] LoadSceneAsync Request '106909442d7b97449b2a77148b5827ef', WaitForSceneLoad: True, SceneEntity: 1697:1
Loading GameData SubScene 106909442d7b97449b2a77148b5827ef (Entity(1697:1)), GameData Scene Entity: Entity(1697:1)
[Server] LoadSceneAsync Request 'de0ffb819455f8f4c8eea0748b1012b6', WaitForSceneLoad: True, SceneEntity: 1699:1
Loading GameData SubScene de0ffb819455f8f4c8eea0748b1012b6 (Entity(1699:1)), GameData Scene Entity: Entity(1699:1)
[Server] LoadSceneAsync Request '5e3be03ad2064bc4a9644c4a1a30af42', WaitForSceneLoad: True, SceneEntity: 1701:1
Loading GameData SubScene 5e3be03ad2064bc4a9644c4a1a30af42 (Entity(1701:1)), GameData Scene Entity: Entity(1701:1)
[Server] LoadSceneAsync Request '0abe506782129014ab07e452031e6f68', WaitForSceneLoad: True, SceneEntity: 1703:1
Loading GameData SubScene 0abe506782129014ab07e452031e6f68 (Entity(1703:1)), GameData Scene Entity: Entity(1703:1)
[Server] LoadSceneAsync Request '295c92b7a799a3443b40e9ef192b5727', WaitForSceneLoad: True, SceneEntity: 1705:1
Loading GameData SubScene 295c92b7a799a3443b40e9ef192b5727 (Entity(1705:1)), GameData Scene Entity: Entity(1705:1)
[Server] LoadSceneAsync Request 'ad37c0edd53365b40acb48df81e07046', WaitForSceneLoad: True, SceneEntity: 1707:1
Loading GameData SubScene ad37c0edd53365b40acb48df81e07046 (Entity(1707:1)), GameData Scene Entity: Entity(1707:1)
[Server] LoadSceneAsync Request '8137c0edd53365b40acb48df81e07046', WaitForSceneLoad: True, SceneEntity: 1709:1
Loading GameData SubScene 8137c0edd53365b40acb48df81e07046 (Entity(1709:1)), GameData Scene Entity: Entity(1709:1)
[Server] LoadSceneAsync Request '153b723300728974d8e2bad70c3fd1f2', WaitForSceneLoad: True, SceneEntity: 1711:1
Loading GameData SubScene 153b723300728974d8e2bad70c3fd1f2 (Entity(1711:1)), GameData Scene Entity: Entity(1711:1)
[Server] LoadSceneAsync Request '3429e7b0ff91dfa4db4318bcdb0e63bc', WaitForSceneLoad: True, SceneEntity: 1713:1
Loading GameData SubScene 3429e7b0ff91dfa4db4318bcdb0e63bc (Entity(1713:1)), GameData Scene Entity: Entity(1713:1)
[Server] LoadSceneAsync Request '4361b3a547128724892dae4688ec7146', WaitForSceneLoad: True, SceneEntity: 1715:1
Loading GameData SubScene 4361b3a547128724892dae4688ec7146 (Entity(1715:1)), GameData Scene Entity: Entity(1715:1)
[Server] LoadSceneAsync Request 'b2f35358da120c04c92b9256f40d76ce', WaitForSceneLoad: True, SceneEntity: 1717:1
Loading GameData SubScene b2f35358da120c04c92b9256f40d76ce (Entity(1717:1)), GameData Scene Entity: Entity(1717:1)
[Server] LoadSceneAsync Request 'WorldMap', WaitForSceneLoad: True, SceneEntity: 1719:1
[Steam] FinalizeSetupServer - Setting Tags
Server Settings hash: 49c154ed4dd96dac8bf6af304402b51d6df8c5ded265ac1e1c9aa64777206797
Size of ServerSettings: 1432
Server Setup Complete
PlatformSystemBase - Server connected to Steam successfully!
PlatformSystemBase - OnPolicyResponse - Game server SteamID: 85568397782118122
PlatformSystemBase - OnPolicyResponse - Game Server VAC Secure!
PlatformSystemBase - OnPolicyResponse - Public IP: *************
PlatformSystemBase - SetServerData - Init!
Streamed scene with   14ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5b2998a2f0961324d880ab4afec89fe2.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1692:1 done!
[Server] World Asset Initialized
Streamed scene with   88ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8137c0edd53365b40acb48df81e07046.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1709:1 done!
Removing SceneTags from GameData Scene Entity Entity(1709:1)
[Server] Registering 1189 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 1189 Prefabs in PrefabCollectionSystem
Streamed scene with   74ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/153b723300728974d8e2bad70c3fd1f2.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1711:1 done!
Removing SceneTags from GameData Scene Entity Entity(1711:1)
[Server] Registering 3687 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 3687 Prefabs in PrefabCollectionSystem
Streamed scene with    5ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3429e7b0ff91dfa4db4318bcdb0e63bc.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1713:1 done!
Removing SceneTags from GameData Scene Entity Entity(1713:1)
[Server] Registering 1558 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 1558 Prefabs in PrefabCollectionSystem
Streamed scene with   20ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4361b3a547128724892dae4688ec7146.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1715:1 done!
Removing SceneTags from GameData Scene Entity Entity(1715:1)
[Server] Registering 395 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 395 Prefabs in PrefabCollectionSystem
Instantiating SingletonPrefab -571920625
Streamed scene with   52ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b2f35358da120c04c92b9256f40d76ce.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1717:1 done!
Removing SceneTags from GameData Scene Entity Entity(1717:1)
[Server] Registering 4841 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 4839 Prefabs in PrefabCollectionSystem
Instantiating SingletonPrefab 1870987059
Instantiating SingletonPrefab -1121711197
Instantiating SingletonPrefab 1601806240
Instantiating SingletonPrefab -64904809
Instantiating SingletonPrefab 957647889
Instantiating SingletonPrefab 1093894748
Instantiating SingletonPrefab 1517300989
Instantiating SingletonPrefab -2067847121
Instantiating SingletonPrefab 1783731105
Instantiating SingletonPrefab -1830466155
Streamed scene with   64ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ad37c0edd53365b40acb48df81e07046.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1707:1 done!
Removing SceneTags from GameData Scene Entity Entity(1707:1)
[Server] Registering 2018 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 2018 Prefabs in PrefabCollectionSystem
Streamed scene with  480ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/295c92b7a799a3443b40e9ef192b5727.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1705:1 done!
Removing SceneTags from GameData Scene Entity Entity(1705:1)
[Server] Registering 8505 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 8505 Prefabs in PrefabCollectionSystem
Instantiating SingletonPrefab -1077964245
Streamed scene with    3ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0abe506782129014ab07e452031e6f68.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1703:1 done!
Removing SceneTags from GameData Scene Entity Entity(1703:1)
Streamed scene with    2ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5e3be03ad2064bc4a9644c4a1a30af42.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1701:1 done!
Removing SceneTags from GameData Scene Entity Entity(1701:1)
[Server] Registering 364 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 364 Prefabs in PrefabCollectionSystem
Streamed scene with    5ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/de0ffb819455f8f4c8eea0748b1012b6.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1699:1 done!
Removing SceneTags from GameData Scene Entity Entity(1699:1)
[Server] Registering 977 Prefab Collection Entries in PrefabCollectionSystem
[Server] Registering 977 Prefabs in PrefabCollectionSystem
Registered GenerateCastlePrefabEntities PrefabGuid(1785107561).
Instantiating SingletonPrefab -1354241064
Instantiating SingletonPrefab -1080364537
Instantiating SingletonPrefab 1162706216
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/106909442d7b97449b2a77148b5827ef.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1697:1 done!
Removing SceneTags from GameData Scene Entity Entity(1697:1)
[Server] GameData Initialized. 11 GameData Scenes Loaded.
#0: Entity(1709:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#1: Entity(1711:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#2: Entity(1713:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#3: Entity(1715:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#4: Entity(1717:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#5: Entity(1707:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#6: Entity(1705:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#7: Entity(1703:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#8: Entity(1701:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#9: Entity(1699:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
#10: Entity(1697:1)  - Unity.Entities.Entity  - Unity.Entities.RequestSceneLoaded  - Unity.Scenes.ResolvedSceneHash  - Unity.Entities.SceneReference  - Unity.Entities.LinkedEntityGroup [Buffer]  - Unity.Scenes.ResolvedSectionEntity [Buffer]  - Unity.Entities.PostLoadCommandBuffer  - ProjectM.GameDataSubSceneTag  - ProjectM.GameDataSubSceneSystem+HasRemovedSceneTags  - Unity.Entities.Simulate
[Server] Registering 1 Asset Metadata Collections in PrefabCollectionSystem
Excluding all archetypes with 177 components from Persistence:
MainMenuCanvasBase
* UICanvasBase
* VisualizeCastleRoomsSingleton
* ShowCrowdedness
* ServantPerkCollectionSingleton
* ShowDeadEndTilesResult
* ShowProjectedSunblockers
* Singleton
* TravelBuffCollection
* ChunkDataRemappings
* LocalRagdollImpulse
* Sun
* CooldownRecoveryRateChangedEvent
* ServantMissionSettingsSingleton
* ServerResetScheduleNoticeSettingsSingleton
* JewelSpawnSystemData
* MarkerGuidanceSingleton
* ServerDebugLogs
* ServerDebugViewData
* MiscLocalizationKeysSingleton
* Singleton
* ShowCommandBufferStats
* KeepDataAfterPersistenceTestSingleton
* TestPersistenceV2Save
* SavePersistentDataEvent
* QuickStartup
* ShowAim
* SpawnChainDebugEnabled
* QueuedSpawnRequestSingleton
* TimeScale
* GameIsPaused
* GameIsPlaying
* UISequenceMappingTag
* UserConnectionChangedEvent
* Singleton
* LightningStormTypeCollection
* CastleBlockCollection
* GenerateCastlePrefabs
* Data
* CastleRebuildSettings
* Singleton
* CastleRebuildRegistry
* CastleRebuildRegistry_Server
* ContestCollection
* FakeTurnOffStudioListener
* DestroyedEntity
* NetworkEventType
* CastleIndexBufferSingleton
* PacketBookkeeperSystemData
* PrioritizeSystemData
* LogPrioritization
* Singleton
* SpellModSyncSystem_ServerData
* AchievementCollectionSingleton
* Singleton
* Singleton
* AbilitySystemDamageTakenEventSinceLastUpdateData
* BlinkSystemData
* InventoryRouteSyncSingleton
* UtcTicksSingleton
* CastleArenaBuildPreviewData
* DefaultEquipmentCollectionSingleton
* LoadToTargetWorld
* StripForBuild
* CollisionDetectionSingleton
* KnockbackAssetsSingleton
* InventoryRouteSingleton_Server
* Singleton
* ShowTileCollision
* RegisterCurveBlobEvent
* LogEntitySpawnDestroy
* SyncedServerDebugSettings
* GameDataInitializedSingleton
* GameDataLoadingStartedSingleton
* PersistenceDebuggingSingleton
* RootPrefabCollection
* ServerRootPrefabCollection
* SystemData
* ServerRuntimeSettings
* SubSceneRemapping
* ModificationsRegistry
* ModificationCleanupSingleton
* MegaStaticTag
* ShowWorldPathsEnabled
* GameplayEventsData
* CurveCollection
* FactionLookupSingleton
* GameDatas
* Singleton
* ProgressionDependencyData
* ServerGameBalanceSettings
* RemappedPrefabDataLookup
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* TimeZonedDateTime
* MapZoneCollection
* UserInfoBufferSingleton
* AiDebugEnabled
* WorldFrame
* MapZoneData
* MapZoneDebugEnabled
* LoadTerrainChunkRequest
* TerrainChunkMetadata
* TerrainChunkLoadedEvent
* TerrainChunkUnloadedEvent
* TerrainChunkMetadataLoadedEvent
* TerrainChunkLookup
* SystemData
* WorldConfigSingleton
* WorldRoadGraph
* DebugShowPhysicsCasts
* GameplayEventDebuggingData
* ManagedEditorGameplayScriptingData
* DestroyedNetworkIdSingleton
* Singleton
* UserActivityGrid
* EnableRoomRoofDebuggingSingleton
* TileWorldSingleton
* SharedCastleInventoryInstance
* SpellModCollectionData
* SpellModTierCollectionData
* WarEventDynamicHealthScalingSingleton
* StaticSceneTag
* RetainBlobAssets
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Prefab
* SceneSectionData
* RequestSceneLoaded
* WorldTime
* SystemInstance
* PhysicsStep
* SimulationSingleton
* PhysicsWorldSingleton
* StepInputSingleton
* AggroChangeLogQueue
* SystemData
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* Singleton
* CastleBuffsSettings
* QueuedTerrainChunkLoadedEvent
* ShowPhysicsColliders
* PhysicsDebugDisplayData
* DebugStruct
* ClaimedBundlesFromOtherWorld
* NetworkSyncDebugEnabled
* ServerShutdown
* ShowUserActivityGridComponent
* ReservedEntityIndex
* WorldTypeSingleton
* Singleton
* SceneSystemConfiguration
* DontSaveEntity
* IdleInteractionBuffBuffer
* RelicDropped
* MostRecentFixedTime
* PublicEntityRef
* SectionMetadataSetup
* SceneSection
PersistenceV2 - Successfully serialized Persistence Header. Header was 819719 bytes large.
[Server] Request Load Chunk 2,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 3,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 4,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 5,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 6,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 7,0, GUID: 672ca7a93fee20b46a9c7d87ff39a4c8
[Server] Request Load Chunk 10,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 12,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 14,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 16,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 18,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 20,0, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 9,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 10,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 11,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 12,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 13,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 14,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 15,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 16,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 17,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 18,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 19,1, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 20,1, GUID: a32a1d347a4b31d4c90744a4ede6acc5
[Server] Request Load Chunk 24,1, GUID: bb1415ea81c843e4ba034af72f10c36b
[Server] Request Load Chunk 7,2, GUID: a8f9b2d179bbbf246963926a0f537def
[Server] Request Load Chunk 9,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 10,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 11,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 12,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 13,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 14,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 15,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 16,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 17,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 18,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 19,2, GUID: 5dd2d5dbc5240404ab98da395aa11c1f
[Server] Request Load Chunk 20,2, GUID: c5d45555f3d519943a0e80adc9ff4f51
[Server] Request Load Chunk 24,2, GUID: 1c35ac5c585935c4cbb47e9b8d285c35
[Server] Request Load Chunk 13,6, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 14,6, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 7,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 8,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 9,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 10,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 11,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 12,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 13,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 14,7, GUID: 0a2fb7d5434b2154fa9f4c1197b74eaf
[Server] Request Load Chunk 15,7, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,8, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 7,8, GUID: 0a2fb7d5434b2154fa9f4c1197b74eaf
[Server] Request Load Chunk 8,8, GUID: 246501a0fbb9b7c4da29aa602ea5c4fa
[Server] Request Load Chunk 9,8, GUID: 2924653d35377074fb8b64f8e6ce332d
[Server] Request Load Chunk 10,8, GUID: 7bdbb53c239283d41ba2e80c2cfc88d1
[Server] Request Load Chunk 11,8, GUID: d0bbfaf22bd883e4893b0ac0b85c9ff3
[Server] Request Load Chunk 12,8, GUID: b13e05079fb92c142b38b178720b3b94
[Server] Request Load Chunk 13,8, GUID: 5689b37fb2a722548923851c2267340e
[Server] Request Load Chunk 14,8, GUID: 246501a0fbb9b7c4da29aa602ea5c4fa
[Server] Request Load Chunk 15,8, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,9, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 7,9, GUID: bb682846556abae47aea289e7a73fe33
[Server] Request Load Chunk 8,9, GUID: 961d54a8261f07443a0714460e6ed3ad
[Server] Request Load Chunk 9,9, GUID: 3739a7322d5189b44b6fc10ff2136d13
[Server] Request Load Chunk 10,9, GUID: 63ec8da752096ff4fbe860e2a27bd938
[Server] Request Load Chunk 11,9, GUID: db1bf1eff344b4740bd20bcd9f3798f2
[Server] Request Load Chunk 12,9, GUID: 7409c40e09fe36849a7a4f69b9dee9a4
[Server] Request Load Chunk 13,9, GUID: 572f5d77f8d8c99478f666fd7dd9e971
[Server] Request Load Chunk 14,9, GUID: c6f842b31ef5c68438513e1ae93b16e9
[Server] Request Load Chunk 15,9, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 16,9, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 5,10, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,10, GUID: 2b0aa0e451049674fa790cced730bbcc
[Server] Request Load Chunk 7,10, GUID: a998f7c4607ba784faf0ff5c29bba6a7
[Server] Request Load Chunk 8,10, GUID: e2cc62feb26335042aa536083bb4dcf0
[Server] Request Load Chunk 9,10, GUID: 1ca277e0bf4d98a43a56cef775b49142
[Server] Request Load Chunk 10,10, GUID: 018be26374d7ad94d99c57e637f5cc42
[Server] Request Load Chunk 11,10, GUID: f25c3a0b1519dcf4ea97a32e97419363
[Server] Request Load Chunk 12,10, GUID: 932bb97227381554dadbdb61e0d574f0
[Server] Request Load Chunk 13,10, GUID: 13cb16d70e7b99f46b9d729629a7610e
[Server] Request Load Chunk 14,10, GUID: f407776a73ac0f941ac82ca0898d23b3
[Server] Request Load Chunk 15,10, GUID: 7603da2c0ab76ce42bbb76d6973ab774
[Server] Request Load Chunk 16,10, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 5,11, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,11, GUID: 9cabd2dd1cf483248ba5c374ce25a01b
[Server] Request Load Chunk 7,11, GUID: dd9f9625193304d4e8aa818da04393fd
[Server] Request Load Chunk 8,11, GUID: e7016e4482dd90d48a07e11fb7b61e1d
[Server] Request Load Chunk 9,11, GUID: 2f28cba5d2967df46a339f6320e81ff7
[Server] Request Load Chunk 10,11, GUID: bc1c781740a9e934fb0d5c193f437f71
[Server] Request Load Chunk 11,11, GUID: 51dc6e7b03b5ede40bff1ee22119048b
[Server] Request Load Chunk 12,11, GUID: 66ed128ef846d964cb87c330243cd418
[Server] Request Load Chunk 13,11, GUID: f3e9e47d7d0e7934da133472526cf0cd
[Server] Request Load Chunk 14,11, GUID: e6f764ecf5a86194eb79b2d8ecce280c
[Server] Request Load Chunk 15,11, GUID: c006a5ce1938498428651ba4e3bacc61
[Server] Request Load Chunk 16,11, GUID: 67c6a4f74fd0eac4094adb77211b329b
[Server] Request Load Chunk 5,12, GUID: 3b35dfc3f24e9bb45b6da67f70a87fbd
[Server] Request Load Chunk 6,12, GUID: 6acb7f42776a280488cf322cfe2c41a1
[Server] Request Load Chunk 7,12, GUID: f0b9143094f3a874ca1c0abf916dc8e4
[Server] Request Load Chunk 8,12, GUID: ed0419b22d53ffc4bb80e7bba27082a6
[Server] Request Load Chunk 9,12, GUID: d10a2bcae0fa8ec44bdb08c5db2aee69
[Server] Request Load Chunk 10,12, GUID: efae7737e5f4ecb49b515d0b78380b08
[Server] Request Load Chunk 11,12, GUID: b4e0fcd1c502f5b41ae8eb3f13639c46
[Server] Request Load Chunk 12,12, GUID: 8459f04ca1b308b4f84f83c5fcb558f3
[Server] Request Load Chunk 13,12, GUID: 9d82a1fe1f1923c4f97dabd78cfc5e69
[Server] Request Load Chunk 14,12, GUID: b3918aa903880054992b249d88e94721
[Server] Request Load Chunk 15,12, GUID: 75b057013a92e184c9aa6bd62f8e6939
[Server] Request Load Chunk 16,12, GUID: d183bd97afec4ba47a046e022c6e2d6f
[Server] Request Load Chunk 3,13, GUID: 969df75726d785141aac4e3eafc10f58
[Server] Request Load Chunk 4,13, GUID: eb98dfa1d0f03614b8701815acc42dc5
[Server] Request Load Chunk 5,13, GUID: 803a66b8863b65a4b8aa02d809ef2961
[Server] Request Load Chunk 6,13, GUID: e46aa2103acda034e93ce31c0b25ed38
[Server] Request Load Chunk 7,13, GUID: 3756338433cce094daf73fad2456a943
[Server] Request Load Chunk 8,13, GUID: 4e0141c01fc58334aa641d7325cb82ad
[Server] Request Load Chunk 9,13, GUID: 300a10b3b72626145bebf232838caa6b
[Server] Request Load Chunk 10,13, GUID: 1e8af03ee0ab0744c915e4fcd10e2700
[Server] Request Load Chunk 11,13, GUID: 65ebd5dc8069d9e4ca0d9f2cb14b61cc
[Server] Request Load Chunk 12,13, GUID: ce6660cca1c5e8e46a9295c0bd5f83b8
[Server] Request Load Chunk 13,13, GUID: b7eec76dc482f474d831a5b8dab5f464
[Server] Request Load Chunk 14,13, GUID: 6c50437c10040a449a8d7211763a840d
[Server] Request Load Chunk 15,13, GUID: 4a4bb2077467d6b49a0421e25646abc9
[Server] Request Load Chunk 16,13, GUID: 1f3daaeb74b19e14f9b56fca0f1f57f8
[Server] Request Load Chunk 17,13, GUID: 166f8e7e5d582b349ae4bf854c799c1e
[Server] Request Load Chunk 3,14, GUID: 969df75726d785141aac4e3eafc10f58
[Server] Request Load Chunk 4,14, GUID: 31902c41e7682894f9c447957cba74b2
[Server] Request Load Chunk 5,14, GUID: 57768327b6c16aa4cad909805524110f
[Server] Request Load Chunk 6,14, GUID: b2864e1d7aa17c94bbbdbe763bf49b61
[Server] Request Load Chunk 7,14, GUID: 97c0b914358f39740b0172c91e7490ca
[Server] Request Load Chunk 8,14, GUID: 8988d34a4a760e648bb45b73cfc9a65c
[Server] Request Load Chunk 9,14, GUID: d8d39d9cae64a994e964897b1d11a878
[Server] Request Load Chunk 10,14, GUID: 33e116ddbdc3d3f43811112b11dc2b52
[Server] Request Load Chunk 11,14, GUID: 0bf871d9fb36cdd48ad122804d9b1013
[Server] Request Load Chunk 12,14, GUID: f7986e7bb9a9eae4d9cfa79927e3e9bc
[Server] Request Load Chunk 13,14, GUID: 72320aa56209ffe438f18204e35b4d6e
[Server] Request Load Chunk 14,14, GUID: 6bd518e3475c59c4f840c8b7a165bdbd
[Server] Request Load Chunk 15,14, GUID: ebc0e81445889354493ff1dd7819395d
[Server] Request Load Chunk 16,14, GUID: 23502868847bd25438b97f66324f3693
[Server] Request Load Chunk 17,14, GUID: ebb5353de2034c24883753a4c3c187ef
[Server] Request Load Chunk 18,14, GUID: 166f8e7e5d582b349ae4bf854c799c1e
[Server] Request Load Chunk 3,15, GUID: 969df75726d785141aac4e3eafc10f58
[Server] Request Load Chunk 4,15, GUID: 1c57d5145f02c9d42a7038429962a4c6
[Server] Request Load Chunk 5,15, GUID: dc32b732a7079b241b4cda0b0a802de1
[Server] Request Load Chunk 6,15, GUID: 6ca8b6b6c80aadc4d9d137c9aa0bf903
[Server] Request Load Chunk 7,15, GUID: 815b649b859e0f344a9606208de15ed9
[Server] Request Load Chunk 8,15, GUID: 60d3f49d35b449b458b14a86bc33e08a
[Server] Request Load Chunk 9,15, GUID: 94bc1ff1b3e17144cbc7860838bf6c5f
[Server] Request Load Chunk 10,15, GUID: 850fabbda3a0e114b9130af63c2227ff
[Server] Request Load Chunk 11,15, GUID: b415b14d94c45bf47961ea6dfd875cdc
[Server] Request Load Chunk 12,15, GUID: 8282cb929e99f6d40b7ef61fb2ef1fde
[Server] Request Load Chunk 13,15, GUID: 0fbff50debacab047a02110fd1efa602
[Server] Request Load Chunk 14,15, GUID: a18bb66913b043a429432c99bf88a792
[Server] Request Load Chunk 15,15, GUID: 0ffcb79747218cc4ba436947639e2d47
[Server] Request Load Chunk 16,15, GUID: 8f029ad3f1a49cb4d922ba39c9842634
[Server] Request Load Chunk 17,15, GUID: 46dfdacabe3533347a2229c35ab3163f
[Server] Request Load Chunk 18,15, GUID: 6bd24955574d58449beebfd189c1f316
[Server] Request Load Chunk 4,16, GUID: 2075b60fd7945c340ae29d0f813173a1
[Server] Request Load Chunk 5,16, GUID: 63f655160d3bf3b4eb567447d7d29e6c
[Server] Request Load Chunk 6,16, GUID: 67187f191c1498a4abbbee7a2e1423db
[Server] Request Load Chunk 7,16, GUID: 54b7431cc8792644a8bc0bc74df76dc0
[Server] Request Load Chunk 8,16, GUID: 8a8f65afccc7c534ebeacce15877790b
[Server] Request Load Chunk 9,16, GUID: 9cd7bd8f2ec8c584db1c67f02a106911
[Server] Request Load Chunk 10,16, GUID: 7a3e53009e61c694a8f7c364bc78af17
[Server] Request Load Chunk 11,16, GUID: 547bc245331509a41978079c01b83a0c
[Server] Request Load Chunk 12,16, GUID: 20cbebceceb1b7b4685da80b72bd6a2c
[Server] Request Load Chunk 13,16, GUID: 866dbd21c5b737f4ca89f83e65e89e9e
[Server] Request Load Chunk 14,16, GUID: 99d16e55435f70146aa87f52b92eaddb
[Server] Request Load Chunk 15,16, GUID: 08430246543c97044bd481dc79687e95
[Server] Request Load Chunk 16,16, GUID: 7d04c45940098e94ca63b6dadb8fb5d0
[Server] Request Load Chunk 17,16, GUID: 4cb629b2044983849b82c92673ff6241
[Server] Request Load Chunk 18,16, GUID: ca02429a0e02d4e4d83f7ee2b7d0d9c7
[Server] Request Load Chunk 19,16, GUID: c337f7631ce849c46aa134e5c89e5904
[Server] Request Load Chunk 3,17, GUID: e23878794ad006e4d91c43b795ccdf0a
[Server] Request Load Chunk 4,17, GUID: ceef3ebf1383b814db17794e71bb1332
[Server] Request Load Chunk 5,17, GUID: c6d34432693f24046ae5916650cc70ef
[Server] Request Load Chunk 6,17, GUID: a24097bd09125954f9c0d4c23a578185
[Server] Request Load Chunk 7,17, GUID: 81fb587f67a221b4b9d75c385f8e9d63
[Server] Request Load Chunk 8,17, GUID: 47c1e305fdffec34d9c4dfdbd9b75a23
[Server] Request Load Chunk 9,17, GUID: 5b5675b6fbd72104690f7844cc7b9983
[Server] Request Load Chunk 10,17, GUID: 2a0e7ef7deda58645b7a8114ea225c57
[Server] Request Load Chunk 11,17, GUID: d11c48a5bd371924fbdff533cc791b22
[Server] Request Load Chunk 12,17, GUID: d53c71f34e6c99e4da88e5048ddeebbb
[Server] Request Load Chunk 13,17, GUID: b22d2359d6d89ee47b79df873d18309b
[Server] Request Load Chunk 14,17, GUID: f3e926d03d837204e92948f303a3af69
[Server] Request Load Chunk 15,17, GUID: 9703f861393067744acce835eb5d06a6
[Server] Request Load Chunk 16,17, GUID: 2e58ee30f0eafb042ad0f68e6676a303
[Server] Request Load Chunk 17,17, GUID: 4939b1e6cef38424eb7429cff38edc4e
[Server] Request Load Chunk 18,17, GUID: 3d5942862fa3c274fa36c3224f27e136
[Server] Request Load Chunk 3,18, GUID: db8503c537e012a428923b38e08d5475
[Server] Request Load Chunk 4,18, GUID: 0e2ed403c9936624394de4e3bdf614ca
[Server] Request Load Chunk 5,18, GUID: cf9f2a01f0aa21f45b29d712351976ff
[Server] Request Load Chunk 6,18, GUID: d6d16109c9507274a9a5ff5fa22c377f
[Server] Request Load Chunk 7,18, GUID: cc75a5684dcec7b4eab954f1779f2244
[Server] Request Load Chunk 8,18, GUID: f486e66146ad6b14da0f1128762bdea9
[Server] Request Load Chunk 9,18, GUID: 979bc1eb8da27534ebecae3e13f9e6c4
[Server] Request Load Chunk 10,18, GUID: 74d18dec9f001e74690990c5b3a58673
[Server] Request Load Chunk 11,18, GUID: 9224e352fb3e2de4a851d4d563a23253
[Server] Request Load Chunk 12,18, GUID: f1ca236d4521f8744a72cef5561ddd68
[Server] Request Load Chunk 13,18, GUID: 2213d341313eab24eba9a1ab1369deed
[Server] Request Load Chunk 14,18, GUID: 4a128f709dc5a604bb6cd25bbaf7e91c
[Server] Request Load Chunk 15,18, GUID: c52fb37929a2af042a280fc3c3400908
[Server] Request Load Chunk 16,18, GUID: f1aa45887bd2f3244a54be879ca9ddf4
[Server] Request Load Chunk 17,18, GUID: 04feb494f00442b4c81ec75321316203
[Server] Request Load Chunk 18,18, GUID: a261a4925caeb15449d9de1b6b137b78
[Server] Request Load Chunk 3,19, GUID: 0699af76f87f5dc43beed1cd0aaef14a
[Server] Request Load Chunk 4,19, GUID: 1a9af355f78c70e43a7a83a9d33fca7d
[Server] Request Load Chunk 5,19, GUID: d2af243084018944c8262b43a8e897aa
[Server] Request Load Chunk 6,19, GUID: 4597a0b36642baf488f8d8a314a3abbf
[Server] Request Load Chunk 7,19, GUID: 2933ef436b9a5964b8bb48801d8eae81
[Server] Request Load Chunk 8,19, GUID: 2cead2dfbe92e3743a4146633630ac8c
[Server] Request Load Chunk 9,19, GUID: ad4db948f91089948a621826ac9c2bef
[Server] Request Load Chunk 10,19, GUID: da2b4c2abad58f34f8a9f131b869bb2c
[Server] Request Load Chunk 11,19, GUID: 3efa376eea3b2ce4e8c56e6042cb58e1
[Server] Request Load Chunk 12,19, GUID: d7c10c4925dcdac4788e5e7d2cbc5831
[Server] Request Load Chunk 13,19, GUID: 2ad20c16f31ec834a99ab851273159dc
[Server] Request Load Chunk 14,19, GUID: 476260f36f2939a42aef1ec3e94d8e15
[Server] Request Load Chunk 15,19, GUID: d65cbf841aad2444095a24600f1bd7f6
[Server] Request Load Chunk 16,19, GUID: 02859bb7f289b5344b828443bca3bbb7
[Server] Request Load Chunk 3,20, GUID: 0cd9b7ad49f9a9a459f5db1116a79c0f
[Server] Request Load Chunk 4,20, GUID: 5bb3f493780edff49bb14e09334ceee4
[Server] Request Load Chunk 5,20, GUID: f8e554bd0775f4d408b136c7f4577984
[Server] Request Load Chunk 6,20, GUID: 69c4456c9cf9e9048974ad89bb2c142f
[Server] Request Load Chunk 7,20, GUID: 7674d10acab38ce49b474e90cec74ebf
[Server] Request Load Chunk 8,20, GUID: 6446314c69b5d754493de5a14ae3902b
[Server] Request Load Chunk 9,20, GUID: 744630a5439ac0940b7c6fc0d21187cc
[Server] Request Load Chunk 10,20, GUID: 58b7e729bf8cf6a4593f65f61aa768c0
[Server] Request Load Chunk 11,20, GUID: 6ee9860801fd49d42b5ffef67240e971
[Server] Request Load Chunk 12,20, GUID: 350db1c39517b094baa99a7787a056bb
[Server] Request Load Chunk 13,20, GUID: 029844f687657aa4c8e5db4e275c4f91
[Server] Request Load Chunk 14,20, GUID: 412d21116f4dfb64a8afa48f2749fe1c
[Server] Request Load Chunk 15,20, GUID: b629f1d8ee7e02846966ad4936c6fe1c
[Server] Request Load Chunk 16,20, GUID: e54a69ffe7b182a449c99249457fbbfa
[Server] Request Load Chunk 3,21, GUID: 0cd9b7ad49f9a9a459f5db1116a79c0f
[Server] Request Load Chunk 4,21, GUID: 0cd9b7ad49f9a9a459f5db1116a79c0f
[Server] Request Load Chunk 5,21, GUID: c918ebcafeb9d64418fe120b947ddc13
[Server] Request Load Chunk 6,21, GUID: 37212c413eefe3945909a2545f9984a3
[Server] Request Load Chunk 7,21, GUID: cd0102376e0df9141a90ac6dcb400ca6
[Server] Request Load Chunk 8,21, GUID: 8009c3c2f451dcc4b8abacf193377c7e
[Server] Request Load Chunk 9,21, GUID: e3013bf393f633f42aeb359b82384b57
[Server] Request Load Chunk 10,21, GUID: 183d5e702836ed748ae813d4757a065a
[Server] Request Load Chunk 11,21, GUID: 3942e8aad1d9f164484fd7c16cfcfc1d
[Server] Request Load Chunk 12,21, GUID: 97fce44cfe63ddf40ae54dd6696ef09e
[Server] Request Load Chunk 13,21, GUID: f508b19e57277a546b8d838d03301fe7
[Server] Request Load Chunk 14,21, GUID: 1495eac6e565a964a9ba08d87878f77b
[Server] Request Load Chunk 4,22, GUID: 0cd9b7ad49f9a9a459f5db1116a79c0f
[Server] Request Load Chunk 5,22, GUID: 2ce7c59433c1e2643ac9e82c5da0db88
[Server] Request Load Chunk 6,22, GUID: 27ada8e0788be0e4591f6692f493ff88
[Server] Request Load Chunk 7,22, GUID: 63e39e9c27341ef4687c8f613c27136e
[Server] Request Load Chunk 8,22, GUID: 9cda48d69859871479364aa38b6e44ea
Streamed scene with   27ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d7852543318383546944f7bcd4a089b6.0.entities
[Server] LoadSceneSystem.WaitForTargetSceneLoad - SceneEntity 1719:1 done!
Streamed scene with   46ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 10,0
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 16,0
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3942e8aad1d9f164484fd7c16cfcfc1d.0.entities
[Server] Loaded Chunk 11,21
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/97fce44cfe63ddf40ae54dd6696ef09e.0.entities
[Server] Loaded Chunk 12,21
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f508b19e57277a546b8d838d03301fe7.0.entities
[Server] Loaded Chunk 13,21
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0cd9b7ad49f9a9a459f5db1116a79c0f.0.entities
[Server] Loaded Chunk 4,22
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2ce7c59433c1e2643ac9e82c5da0db88.0.entities
[Server] Loaded Chunk 5,22
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/27ada8e0788be0e4591f6692f493ff88.0.entities
[Server] Loaded Chunk 6,22
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/63e39e9c27341ef4687c8f613c27136e.0.entities
[Server] Loaded Chunk 7,22
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9cda48d69859871479364aa38b6e44ea.0.entities
[Server] Loaded Chunk 8,22
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e54a69ffe7b182a449c99249457fbbfa.0.entities
[Server] Loaded Chunk 16,20
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0cd9b7ad49f9a9a459f5db1116a79c0f.0.entities
[Server] Loaded Chunk 3,21
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0cd9b7ad49f9a9a459f5db1116a79c0f.0.entities
[Server] Loaded Chunk 4,21
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c918ebcafeb9d64418fe120b947ddc13.0.entities
[Server] Loaded Chunk 5,21
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/cd0102376e0df9141a90ac6dcb400ca6.0.entities
[Server] Loaded Chunk 7,21
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e3013bf393f633f42aeb359b82384b57.0.entities
[Server] Loaded Chunk 9,21
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0cd9b7ad49f9a9a459f5db1116a79c0f.0.entities
[Server] Loaded Chunk 3,20
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4a128f709dc5a604bb6cd25bbaf7e91c.0.entities
[Server] Loaded Chunk 14,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4597a0b36642baf488f8d8a314a3abbf.0.entities
[Server] Loaded Chunk 6,19
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/db8503c537e012a428923b38e08d5475.0.entities
[Server] Loaded Chunk 3,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/47c1e305fdffec34d9c4dfdbd9b75a23.0.entities
[Server] Loaded Chunk 8,17
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a24097bd09125954f9c0d4c23a578185.0.entities
[Server] Loaded Chunk 6,17
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c6d34432693f24046ae5916650cc70ef.0.entities
[Server] Loaded Chunk 5,17
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ceef3ebf1383b814db17794e71bb1332.0.entities
[Server] Loaded Chunk 4,17
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e23878794ad006e4d91c43b795ccdf0a.0.entities
[Server] Loaded Chunk 3,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7d04c45940098e94ca63b6dadb8fb5d0.0.entities
[Server] Loaded Chunk 16,16
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2075b60fd7945c340ae29d0f813173a1.0.entities
[Server] Loaded Chunk 4,16
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6bd24955574d58449beebfd189c1f316.0.entities
[Server] Loaded Chunk 18,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a18bb66913b043a429432c99bf88a792.0.entities
[Server] Loaded Chunk 14,15
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/969df75726d785141aac4e3eafc10f58.0.entities
[Server] Loaded Chunk 3,15
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/166f8e7e5d582b349ae4bf854c799c1e.0.entities
[Server] Loaded Chunk 18,14
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6c50437c10040a449a8d7211763a840d.0.entities
[Server] Loaded Chunk 14,13
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1f3daaeb74b19e14f9b56fca0f1f57f8.0.entities
[Server] Loaded Chunk 16,13
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/166f8e7e5d582b349ae4bf854c799c1e.0.entities
[Server] Loaded Chunk 17,13
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/969df75726d785141aac4e3eafc10f58.0.entities
[Server] Loaded Chunk 3,14
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ce6660cca1c5e8e46a9295c0bd5f83b8.0.entities
[Server] Loaded Chunk 12,13
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/65ebd5dc8069d9e4ca0d9f2cb14b61cc.0.entities
[Server] Loaded Chunk 11,13
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4e0141c01fc58334aa641d7325cb82ad.0.entities
[Server] Loaded Chunk 8,13
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3756338433cce094daf73fad2456a943.0.entities
[Server] Loaded Chunk 7,13
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/803a66b8863b65a4b8aa02d809ef2961.0.entities
[Server] Loaded Chunk 5,13
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/eb98dfa1d0f03614b8701815acc42dc5.0.entities
[Server] Loaded Chunk 4,13
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/969df75726d785141aac4e3eafc10f58.0.entities
[Server] Loaded Chunk 3,13
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8459f04ca1b308b4f84f83c5fcb558f3.0.entities
[Server] Loaded Chunk 12,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/efae7737e5f4ecb49b515d0b78380b08.0.entities
[Server] Loaded Chunk 10,12
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ed0419b22d53ffc4bb80e7bba27082a6.0.entities
[Server] Loaded Chunk 8,12
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 5,12
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/67c6a4f74fd0eac4094adb77211b329b.0.entities
[Server] Loaded Chunk 16,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/dd9f9625193304d4e8aa818da04393fd.0.entities
[Server] Loaded Chunk 7,11
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9cabd2dd1cf483248ba5c374ce25a01b.0.entities
[Server] Loaded Chunk 6,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 5,11
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 16,10
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f407776a73ac0f941ac82ca0898d23b3.0.entities
[Server] Loaded Chunk 14,10
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 5,10
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 16,9
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 15,9
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c6f842b31ef5c68438513e1ae93b16e9.0.entities
[Server] Loaded Chunk 14,9
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/bb682846556abae47aea289e7a73fe33.0.entities
[Server] Loaded Chunk 7,9
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 6,9
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 15,8
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5689b37fb2a722548923851c2267340e.0.entities
[Server] Loaded Chunk 13,8
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b13e05079fb92c142b38b178720b3b94.0.entities
[Server] Loaded Chunk 12,8
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d0bbfaf22bd883e4893b0ac0b85c9ff3.0.entities
[Server] Loaded Chunk 11,8
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7bdbb53c239283d41ba2e80c2cfc88d1.0.entities
[Server] Loaded Chunk 10,8
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2924653d35377074fb8b64f8e6ce332d.0.entities
[Server] Loaded Chunk 9,8
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 6,8
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 15,7
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 13,7
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 12,7
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 11,7
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 10,7
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 9,7
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 8,7
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 7,7
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 6,7
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 14,6
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 13,6
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 19,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 17,2
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 15,2
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 13,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 11,2
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5dd2d5dbc5240404ab98da395aa11c1f.0.entities
[Server] Loaded Chunk 9,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a8f9b2d179bbbf246963926a0f537def.0.entities
[Server] Loaded Chunk 7,2
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 19,1
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 17,1
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 15,1
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 13,1
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 11,1
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 9,1
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 20,0
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 18,0
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 14,0
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3b35dfc3f24e9bb45b6da67f70a87fbd.0.entities
[Server] Loaded Chunk 12,0
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/bb1415ea81c843e4ba034af72f10c36b.0.entities
[Server] Loaded Chunk 24,1
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1495eac6e565a964a9ba08d87878f77b.0.entities
[Server] Loaded Chunk 14,21
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/029844f687657aa4c8e5db4e275c4f91.0.entities
[Server] Loaded Chunk 13,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/412d21116f4dfb64a8afa48f2749fe1c.0.entities
[Server] Loaded Chunk 14,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/37212c413eefe3945909a2545f9984a3.0.entities
[Server] Loaded Chunk 6,21
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8009c3c2f451dcc4b8abacf193377c7e.0.entities
[Server] Loaded Chunk 8,21
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/183d5e702836ed748ae813d4757a065a.0.entities
[Server] Loaded Chunk 10,21
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2933ef436b9a5964b8bb48801d8eae81.0.entities
[Server] Loaded Chunk 7,19
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2cead2dfbe92e3743a4146633630ac8c.0.entities
[Server] Loaded Chunk 8,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ad4db948f91089948a621826ac9c2bef.0.entities
[Server] Loaded Chunk 9,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3efa376eea3b2ce4e8c56e6042cb58e1.0.entities
[Server] Loaded Chunk 11,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d7c10c4925dcdac4788e5e7d2cbc5831.0.entities
[Server] Loaded Chunk 12,19
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/476260f36f2939a42aef1ec3e94d8e15.0.entities
[Server] Loaded Chunk 14,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d65cbf841aad2444095a24600f1bd7f6.0.entities
[Server] Loaded Chunk 15,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5bb3f493780edff49bb14e09334ceee4.0.entities
[Server] Loaded Chunk 4,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7674d10acab38ce49b474e90cec74ebf.0.entities
[Server] Loaded Chunk 7,20
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6446314c69b5d754493de5a14ae3902b.0.entities
[Server] Loaded Chunk 8,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/744630a5439ac0940b7c6fc0d21187cc.0.entities
[Server] Loaded Chunk 9,20
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6ee9860801fd49d42b5ffef67240e971.0.entities
[Server] Loaded Chunk 11,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/350db1c39517b094baa99a7787a056bb.0.entities
[Server] Loaded Chunk 12,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c52fb37929a2af042a280fc3c3400908.0.entities
[Server] Loaded Chunk 15,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f1aa45887bd2f3244a54be879ca9ddf4.0.entities
[Server] Loaded Chunk 16,18
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/04feb494f00442b4c81ec75321316203.0.entities
[Server] Loaded Chunk 17,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a261a4925caeb15449d9de1b6b137b78.0.entities
[Server] Loaded Chunk 18,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0699af76f87f5dc43beed1cd0aaef14a.0.entities
[Server] Loaded Chunk 3,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1a9af355f78c70e43a7a83a9d33fca7d.0.entities
[Server] Loaded Chunk 4,19
Streamed scene with   11ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d2af243084018944c8262b43a8e897aa.0.entities
[Server] Loaded Chunk 5,19
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2213d341313eab24eba9a1ab1369deed.0.entities
[Server] Loaded Chunk 13,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f1ca236d4521f8744a72cef5561ddd68.0.entities
[Server] Loaded Chunk 12,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9224e352fb3e2de4a851d4d563a23253.0.entities
[Server] Loaded Chunk 11,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/979bc1eb8da27534ebecae3e13f9e6c4.0.entities
[Server] Loaded Chunk 9,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/cc75a5684dcec7b4eab954f1779f2244.0.entities
[Server] Loaded Chunk 7,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d6d16109c9507274a9a5ff5fa22c377f.0.entities
[Server] Loaded Chunk 6,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/cf9f2a01f0aa21f45b29d712351976ff.0.entities
[Server] Loaded Chunk 5,18
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3d5942862fa3c274fa36c3224f27e136.0.entities
[Server] Loaded Chunk 18,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4939b1e6cef38424eb7429cff38edc4e.0.entities
[Server] Loaded Chunk 17,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2e58ee30f0eafb042ad0f68e6676a303.0.entities
[Server] Loaded Chunk 16,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f3e926d03d837204e92948f303a3af69.0.entities
[Server] Loaded Chunk 14,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b22d2359d6d89ee47b79df873d18309b.0.entities
[Server] Loaded Chunk 13,17
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d53c71f34e6c99e4da88e5048ddeebbb.0.entities
[Server] Loaded Chunk 12,17
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d11c48a5bd371924fbdff533cc791b22.0.entities
[Server] Loaded Chunk 11,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2a0e7ef7deda58645b7a8114ea225c57.0.entities
[Server] Loaded Chunk 10,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/5b5675b6fbd72104690f7844cc7b9983.0.entities
[Server] Loaded Chunk 9,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/81fb587f67a221b4b9d75c385f8e9d63.0.entities
[Server] Loaded Chunk 7,17
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ca02429a0e02d4e4d83f7ee2b7d0d9c7.0.entities
[Server] Loaded Chunk 18,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/08430246543c97044bd481dc79687e95.0.entities
[Server] Loaded Chunk 15,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/99d16e55435f70146aa87f52b92eaddb.0.entities
[Server] Loaded Chunk 14,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/866dbd21c5b737f4ca89f83e65e89e9e.0.entities
[Server] Loaded Chunk 13,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/20cbebceceb1b7b4685da80b72bd6a2c.0.entities
[Server] Loaded Chunk 12,16
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/547bc245331509a41978079c01b83a0c.0.entities
[Server] Loaded Chunk 11,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7a3e53009e61c694a8f7c364bc78af17.0.entities
[Server] Loaded Chunk 10,16
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8a8f65afccc7c534ebeacce15877790b.0.entities
[Server] Loaded Chunk 8,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/54b7431cc8792644a8bc0bc74df76dc0.0.entities
[Server] Loaded Chunk 7,16
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/63f655160d3bf3b4eb567447d7d29e6c.0.entities
[Server] Loaded Chunk 5,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/46dfdacabe3533347a2229c35ab3163f.0.entities
[Server] Loaded Chunk 17,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8f029ad3f1a49cb4d922ba39c9842634.0.entities
[Server] Loaded Chunk 16,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0fbff50debacab047a02110fd1efa602.0.entities
[Server] Loaded Chunk 13,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b415b14d94c45bf47961ea6dfd875cdc.0.entities
[Server] Loaded Chunk 11,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/850fabbda3a0e114b9130af63c2227ff.0.entities
[Server] Loaded Chunk 10,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/94bc1ff1b3e17144cbc7860838bf6c5f.0.entities
[Server] Loaded Chunk 9,15
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/60d3f49d35b449b458b14a86bc33e08a.0.entities
[Server] Loaded Chunk 8,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6ca8b6b6c80aadc4d9d137c9aa0bf903.0.entities
[Server] Loaded Chunk 6,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/dc32b732a7079b241b4cda0b0a802de1.0.entities
[Server] Loaded Chunk 5,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1c57d5145f02c9d42a7038429962a4c6.0.entities
[Server] Loaded Chunk 4,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ebb5353de2034c24883753a4c3c187ef.0.entities
[Server] Loaded Chunk 17,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/23502868847bd25438b97f66324f3693.0.entities
[Server] Loaded Chunk 16,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/ebc0e81445889354493ff1dd7819395d.0.entities
[Server] Loaded Chunk 15,14
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6bd518e3475c59c4f840c8b7a165bdbd.0.entities
[Server] Loaded Chunk 14,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/72320aa56209ffe438f18204e35b4d6e.0.entities
[Server] Loaded Chunk 13,14
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f7986e7bb9a9eae4d9cfa79927e3e9bc.0.entities
[Server] Loaded Chunk 12,14
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0bf871d9fb36cdd48ad122804d9b1013.0.entities
[Server] Loaded Chunk 11,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d8d39d9cae64a994e964897b1d11a878.0.entities
[Server] Loaded Chunk 9,14
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8988d34a4a760e648bb45b73cfc9a65c.0.entities
[Server] Loaded Chunk 8,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/31902c41e7682894f9c447957cba74b2.0.entities
[Server] Loaded Chunk 4,14
Streamed scene with   11ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/57768327b6c16aa4cad909805524110f.0.entities
[Server] Loaded Chunk 5,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b2864e1d7aa17c94bbbdbe763bf49b61.0.entities
[Server] Loaded Chunk 6,14
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/97c0b914358f39740b0172c91e7490ca.0.entities
[Server] Loaded Chunk 7,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1e8af03ee0ab0744c915e4fcd10e2700.0.entities
[Server] Loaded Chunk 10,13
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e46aa2103acda034e93ce31c0b25ed38.0.entities
[Server] Loaded Chunk 6,13
Spawn Script_Lucie_PotionDropManager_DataServer - Entity(74142:3)
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d183bd97afec4ba47a046e022c6e2d6f.0.entities
[Server] Loaded Chunk 16,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/75b057013a92e184c9aa6bd62f8e6939.0.entities
[Server] Loaded Chunk 15,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b3918aa903880054992b249d88e94721.0.entities
[Server] Loaded Chunk 14,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9d82a1fe1f1923c4f97dabd78cfc5e69.0.entities
[Server] Loaded Chunk 13,12
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b4e0fcd1c502f5b41ae8eb3f13639c46.0.entities
[Server] Loaded Chunk 11,12
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/d10a2bcae0fa8ec44bdb08c5db2aee69.0.entities
[Server] Loaded Chunk 9,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f0b9143094f3a874ca1c0abf916dc8e4.0.entities
[Server] Loaded Chunk 7,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c006a5ce1938498428651ba4e3bacc61.0.entities
[Server] Loaded Chunk 15,11
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/66ed128ef846d964cb87c330243cd418.0.entities
[Server] Loaded Chunk 12,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/bc1c781740a9e934fb0d5c193f437f71.0.entities
[Server] Loaded Chunk 10,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2f28cba5d2967df46a339f6320e81ff7.0.entities
[Server] Loaded Chunk 9,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e7016e4482dd90d48a07e11fb7b61e1d.0.entities
[Server] Loaded Chunk 8,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/13cb16d70e7b99f46b9d729629a7610e.0.entities
[Server] Loaded Chunk 13,10
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f25c3a0b1519dcf4ea97a32e97419363.0.entities
[Server] Loaded Chunk 11,10
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e2cc62feb26335042aa536083bb4dcf0.0.entities
[Server] Loaded Chunk 8,10
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a998f7c4607ba784faf0ff5c29bba6a7.0.entities
[Server] Loaded Chunk 7,10
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/572f5d77f8d8c99478f666fd7dd9e971.0.entities
[Server] Loaded Chunk 13,9
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7409c40e09fe36849a7a4f69b9dee9a4.0.entities
[Server] Loaded Chunk 12,9
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/db1bf1eff344b4740bd20bcd9f3798f2.0.entities
[Server] Loaded Chunk 11,9
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/63ec8da752096ff4fbe860e2a27bd938.0.entities
[Server] Loaded Chunk 10,9
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/3739a7322d5189b44b6fc10ff2136d13.0.entities
[Server] Loaded Chunk 9,9
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/961d54a8261f07443a0714460e6ed3ad.0.entities
[Server] Loaded Chunk 8,9
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0a2fb7d5434b2154fa9f4c1197b74eaf.0.entities
[Server] Loaded Chunk 7,8
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0a2fb7d5434b2154fa9f4c1197b74eaf.0.entities
[Server] Loaded Chunk 14,7
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 20,2
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 18,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 16,2
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 14,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 12,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c5d45555f3d519943a0e80adc9ff4f51.0.entities
[Server] Loaded Chunk 10,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 10,1
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 2,0
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 7,0
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 6,0
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 5,0
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 4,0
Streamed scene with    6ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/672ca7a93fee20b46a9c7d87ff39a4c8.0.entities
[Server] Loaded Chunk 3,0
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/246501a0fbb9b7c4da29aa602ea5c4fa.0.entities
[Server] Loaded Chunk 8,8
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2ad20c16f31ec834a99ab851273159dc.0.entities
[Server] Loaded Chunk 13,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/02859bb7f289b5344b828443bca3bbb7.0.entities
[Server] Loaded Chunk 16,19
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/69c4456c9cf9e9048974ad89bb2c142f.0.entities
[Server] Loaded Chunk 6,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/58b7e729bf8cf6a4593f65f61aa768c0.0.entities
[Server] Loaded Chunk 10,20
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/74d18dec9f001e74690990c5b3a58673.0.entities
[Server] Loaded Chunk 10,18
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f486e66146ad6b14da0f1128762bdea9.0.entities
[Server] Loaded Chunk 8,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0e2ed403c9936624394de4e3bdf614ca.0.entities
[Server] Loaded Chunk 4,18
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9703f861393067744acce835eb5d06a6.0.entities
[Server] Loaded Chunk 15,17
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4cb629b2044983849b82c92673ff6241.0.entities
[Server] Loaded Chunk 17,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/9cd7bd8f2ec8c584db1c67f02a106911.0.entities
[Server] Loaded Chunk 9,16
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/67187f191c1498a4abbbee7a2e1423db.0.entities
[Server] Loaded Chunk 6,16
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/0ffcb79747218cc4ba436947639e2d47.0.entities
[Server] Loaded Chunk 15,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/8282cb929e99f6d40b7ef61fb2ef1fde.0.entities
[Server] Loaded Chunk 12,15
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b7eec76dc482f474d831a5b8dab5f464.0.entities
[Server] Loaded Chunk 13,13
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/4a4bb2077467d6b49a0421e25646abc9.0.entities
[Server] Loaded Chunk 15,13
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/300a10b3b72626145bebf232838caa6b.0.entities
[Server] Loaded Chunk 9,13
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/6acb7f42776a280488cf322cfe2c41a1.0.entities
[Server] Loaded Chunk 6,12
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/e6f764ecf5a86194eb79b2d8ecce280c.0.entities
[Server] Loaded Chunk 14,11
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/51dc6e7b03b5ede40bff1ee22119048b.0.entities
[Server] Loaded Chunk 11,11
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1ca277e0bf4d98a43a56cef775b49142.0.entities
[Server] Loaded Chunk 9,10
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/246501a0fbb9b7c4da29aa602ea5c4fa.0.entities
[Server] Loaded Chunk 14,8
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f8e554bd0775f4d408b136c7f4577984.0.entities
[Server] Loaded Chunk 5,20
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/b629f1d8ee7e02846966ad4936c6fe1c.0.entities
[Server] Loaded Chunk 15,20
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/da2b4c2abad58f34f8a9f131b869bb2c.0.entities
[Server] Loaded Chunk 10,19
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/c337f7631ce849c46aa134e5c89e5904.0.entities
[Server] Loaded Chunk 19,16
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/815b649b859e0f344a9606208de15ed9.0.entities
[Server] Loaded Chunk 7,15
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/33e116ddbdc3d3f43811112b11dc2b52.0.entities
[Server] Loaded Chunk 10,14
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/f3e9e47d7d0e7934da133472526cf0cd.0.entities
[Server] Loaded Chunk 13,11
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/7603da2c0ab76ce42bbb76d6973ab774.0.entities
[Server] Loaded Chunk 15,10
Streamed scene with    9ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/932bb97227381554dadbdb61e0d574f0.0.entities
[Server] Loaded Chunk 12,10
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/018be26374d7ad94d99c57e637f5cc42.0.entities
[Server] Loaded Chunk 10,10
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/2b0aa0e451049674fa790cced730bbcc.0.entities
[Server] Loaded Chunk 6,10
Streamed scene with    7ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/1c35ac5c585935c4cbb47e9b8d285c35.0.entities
[Server] Loaded Chunk 24,2
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 20,1
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 18,1
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 16,1
Streamed scene with    8ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 14,1
Streamed scene with   10ms latency from C:/server/JackSparrow/VRisingServer_Data/StreamingAssets/EntityScenes/a32a1d347a4b31d4c90744a4ede6acc5.0.entities
[Server] Loaded Chunk 12,1
PersistenceV2 - Loading save at C:\server\JackSparrow\save-data\Saves\v4\crepusculo\AutoSave_3.save.gz.
PersistenceV2 - GameVersion of Loaded Save: 1.1.1.91296, Current GameVersion: 1.1.1.91296
PersistenceV2 - Deserialized Header. Persistent Component Types: 1540
PersistenceV2 - Finished Loading 273366 Entities spread over 141 Archetypes.
SpawnTeamSystem_OnPersistenceLoad HighestTeamId: 10
[CompressModificationIdsOnLoadSystem] GetModificationFieldPerType() took 0,0620051s to run!
[CompressModificationIdsOnLoadSystem] ModificationIdRemappingJob took 0,0001666s to run!
[CompressModificationIdsOnLoadSystem] Compressed from 3 ids to 3! A reduction of 0 ids!
[Server] Startup Completed - Disabling Scene Loading Systems
[Server] Shutting down Asynchronous Streaming
Registered WarEventMapNode in Chunk: 16,14
Registered WarEventMapNode in Chunk: 16,15
Registered WarEventMapNode in Chunk: 15,16
Registered WarEventMapNode in Chunk: 18,16
Registered WarEventMapNode in Chunk: 16,17
Registered WarEventMapNode in Chunk: 17,17
Registered WarEventMapNode in Chunk: 15,18
Internal: JobTempAlloc has allocations that are more than the maximum lifespan of 4 frames old - this is not allowed and likely a leak
To Debug, run app with -diag-job-temp-memory-leak-validation cmd line argument. This will output the callstacks of the leaked allocations.
Internal: JobTempAlloc has allocations that are more than the maximum lifespan of 4 frames old - this is not allowed and likely a leak
To Debug, run app with -diag-job-temp-memory-leak-validation cmd line argument. This will output the callstacks of the leaked allocations.
Backtrace native integration status: Can't load Backtrace DLL
UnityEngine.Logger:Log(LogType, Object)
UnityEngine.Debug:LogWarning(Object)
Backtrace.Unity.Runtime.Native.Windows.NativeClient:HandleNativeCrashes(IDictionary`2, IEnumerable`1)
Backtrace.Unity.Runtime.Native.Windows.NativeClient:.ctor(BacktraceConfiguration, BacktraceBreadcrumbs, IDictionary`2, IEnumerable`1)
Backtrace.Unity.Runtime.Native.NativeClientFactory:CreateNativeClient(BacktraceConfiguration, String, BacktraceBreadcrumbs, IDictionary`2, ICollection`1)
Backtrace.Unity.BacktraceClient:Refresh()
Backtrace.Unity.BacktraceClient:Initialize(BacktraceConfiguration, Dictionary`2, String)
ProjectM.BacktraceUtility:Initialize(Boolean, Func`2)
ProjectM.BacktraceSystem:OnUpdate()
Unity.Entities.SystemBase:Update()
Unity.Entities.ComponentSystemGroup:UpdateAllSystems()
Unity.Entities.SystemBase:Update()
Unity.Entities.ComponentSystemGroup:UpdateAllSystems()
Unity.Entities.TimeableComponentSystemGroup:OnUpdate()
Unity.Entities.SystemBase:Update()

GameBootstrap.OnApplicationQuit()
SaveOnExit() - Saving the world on exit!
Triggering AutoSave 4!
SaveOnExit() - Finalizing any potentially active save operations
Resized NetBuffer from length: 4194304 to: 6291456 (additional number of bits: 7680)
Resized NetBuffer from length: 4194304 to: 6291456 (additional number of bits: 12288)
Resized NetBuffer from length: 6291456 to: 9437184 (additional number of bits: 7680)
Resized NetBuffer from length: 6291456 to: 9437184 (additional number of bits: 8192)
Resized NetBuffer from length: 9437184 to: 14155776 (additional number of bits: 8192)
Resized NetBuffer from length: 14155776 to: 21233664 (additional number of bits: 12288)
PersistenceV2 - Finished Saving to 'C:\server\JackSparrow\save-data\Saves\v4\crepusculo\AutoSave_4.save.gz'. Total Persistent ChunkCount 2670. Total Save Size: 7039171 Total Uncompressed Save Size: 33441718 Bytes System Data Size: 1828 Bytes World Data Size: 4194313 Bytes Header Data Size: 819719 Bytes, (Job 0 ArchetypeCount 20, ChunkCount 2124), (Job 1 ArchetypeCount 121, ChunkCount 546), (Job 2 ArchetypeCount 0, ChunkCount 0), (Job 3 ArchetypeCount 0, ChunkCount 0)
SaveOnExit() - Finished saving the world on exit!
Shutdown ServerSteamTransportLayer
Closing Steam Socket: 1
Socket Closed Successfully: 1
Closing Steam Socket: 2
Socket Closed Successfully: 2
ServerBootstrapSystem.OnDestroy
EOSPlatformSystem - Entering OnDestroy!
GameBootstrap.OnDestroy()
SaveAndShutdownServer - Already Shutdown!
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
Memory Statistics:
[ALLOC_TEMP_TLS] TLS Allocator
  StackAllocators : 
    [ALLOC_TEMP_MAIN]
      Peak usage frame count: [0-1.0 KB]: 31 frames, [2.0 KB-4.0 KB]: 2051 frames, [4.0 KB-8.0 KB]: 70 frames, [8.0 KB-16.0 KB]: 2093 frames, [16.0 KB-32.0 KB]: 1 frames, [4.0 MB-8.0 MB]: 1 frames
      Initial Block Size 16.0 MB
      Current Block Size 16.0 MB
      Peak Allocated Bytes 4.7 MB
      Overflow Count 0
    [ALLOC_TEMP_Loading.AsyncRead]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 348 B
      Overflow Count 0
    [ALLOC_TEMP_Loading.PreloadManager]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 142.5 KB
      Overflow Count 4
    [ALLOC_TEMP_Background Job.Worker 8]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 6]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 1.7 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 0]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 11.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 10]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 9]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 5]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 4.2 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 14]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 6]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 4]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 1.9 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 3]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 11.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 12]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_EnlightenWorker] x 2
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 15]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 1]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 1.6 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 2]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 7]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_AssetGarbageCollectorHelper] x 7
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 138 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 5]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 13]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 1]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 11.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 2]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 11.5 KB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 3]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 11]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 0]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 4]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
    [ALLOC_TEMP_BatchDeleteObjects]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 54 B
      Overflow Count 0
[ALLOC_DEFAULT] Dual Thread Allocator
  Peak main deferred allocation count 306
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 2.9 MB
    [ALLOC_DEFAULT_MAIN]
      Peak usage frame count: [256.0 MB-0.50 GB]: 198 frames, [0.50 GB-1.00 GB]: 1466 frames, [1.00 GB-2.00 GB]: 2583 frames
      Requested Block Size 16.0 MB
      Peak Block count 91
      Peak Allocated memory 1.70 GB
      Peak Large allocation bytes 376.2 MB
    [ALLOC_DEFAULT_THREAD]
      Peak usage frame count: [16.0 MB-32.0 MB]: 211 frames, [32.0 MB-64.0 MB]: 374 frames, [64.0 MB-128.0 MB]: 498 frames, [128.0 MB-256.0 MB]: 889 frames, [256.0 MB-0.50 GB]: 2275 frames
      Requested Block Size 16.0 MB
      Peak Block count 21
      Peak Allocated memory 407.2 MB
      Peak Large allocation bytes 135.3 MB
[ALLOC_TEMP_JOB_1_FRAME]
  Initial Block Size 32.0 MB
  Used Block Count 0
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_2_FRAMES]
  Initial Block Size 32.0 MB
  Used Block Count 0
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_4_FRAMES (JobTemp)]
  Initial Block Size 32.0 MB
  Used Block Count 4
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_ASYNC (Background)]
  Initial Block Size 1.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_GFX] Dual Thread Allocator
  Peak main deferred allocation count 0
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 2.9 MB
    [ALLOC_GFX_MAIN]
      Peak usage frame count: [2.0 MB-4.0 MB]: 4247 frames
      Requested Block Size 16.0 MB
      Peak Block count 1
      Peak Allocated memory 2.0 MB
      Peak Large allocation bytes 0 B
    [ALLOC_GFX_THREAD]
      Peak usage frame count: [8.0 KB-16.0 KB]: 4247 frames
      Requested Block Size 16.0 MB
      Peak Block count 1
      Peak Allocated memory 8.2 KB
      Peak Large allocation bytes 0 B
[ALLOC_CACHEOBJECTS] Dual Thread Allocator
  Peak main deferred allocation count 1
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 2.9 MB
    [ALLOC_CACHEOBJECTS_MAIN]
      Peak usage frame count: [4.0 MB-8.0 MB]: 4247 frames
      Requested Block Size 4.0 MB
      Peak Block count 2
      Peak Allocated memory 5.2 MB
      Peak Large allocation bytes 0 B
    [ALLOC_CACHEOBJECTS_THREAD]
      Peak usage frame count: [256.0 KB-0.5 MB]: 4246 frames, [0.5 MB-1.0 MB]: 1 frames
      Requested Block Size 4.0 MB
      Peak Block count 1
      Peak Allocated memory 0.8 MB
      Peak Large allocation bytes 0 B
[ALLOC_TYPETREE] Dual Thread Allocator
  Peak main deferred allocation count 0
    [ALLOC_BUCKET]
      Large Block size 4.0 MB
      Used Block count 1
      Peak Allocated bytes 2.9 MB
    [ALLOC_TYPETREE_MAIN]
      Peak usage frame count: [8.0 KB-16.0 KB]: 4247 frames
      Requested Block Size 2.0 MB
      Peak Block count 1
      Peak Allocated memory 11.9 KB
      Peak Large allocation bytes 0 B
    [ALLOC_TYPETREE_THREAD]
      Peak usage frame count: [8.0 KB-16.0 KB]: 4247 frames
      Requested Block Size 2.0 MB
      Peak Block count 1
      Peak Allocated memory 9.4 KB
      Peak Large allocation bytes 0 B
Internal: There are remaining Allocations on the JobTempAlloc. This is a leak, and will impact performance
To Debug, run app with -diag-job-temp-memory-leak-validation cmd line argument. This will output the callstacks of the leaked allocations.
