#!/usr/bin/env python3
"""
Script para mover arquivos do servidor V Rising para pasta VRisingServer
"""

import os
import shutil
import sys

def move_server_files():
    """Move arquivos do servidor para pasta VRisingServer"""
    
    print("🔄 Movendo arquivos do servidor V Rising...")
    
    # Verificar se estamos na pasta correta
    if not os.path.exists("VRisingServer.exe"):
        print("❌ VRisingServer.exe não encontrado na pasta atual!")
        print("💡 Execute este script na pasta onde está o servidor")
        input("Pressione Enter para sair...")
        return False
    
    # Criar pasta VRisingServer se não existir
    server_folder = "VRisingServer"
    if not os.path.exists(server_folder):
        os.makedirs(server_folder)
        print(f"📁 Pasta '{server_folder}' criada")
    
    # Lista de arquivos e pastas do servidor para mover
    server_items = [
        "VRisingServer.exe",
        "VRisingServer_Data",
        "save-data",
        "logs",
        "backups",
        "BepInEx",
        "winhttp.dll",
        "UnityCrashHandler64.exe",
        "UnityPlayer.dll"
    ]
    
    moved_items = []
    
    for item in server_items:
        if os.path.exists(item):
            try:
                dest_path = os.path.join(server_folder, item)
                
                if os.path.isfile(item):
                    shutil.move(item, dest_path)
                    print(f"📄 Movido: {item}")
                else:
                    shutil.move(item, dest_path)
                    print(f"📁 Movido: {item}/")
                
                moved_items.append(item)
                
            except Exception as e:
                print(f"❌ Erro ao mover {item}: {e}")
        else:
            print(f"⚠️ Não encontrado: {item}")
    
    print(f"\n✅ Movidos {len(moved_items)} itens para '{server_folder}/'")
    
    # Verificar se VRising_Manager existe e não mover
    if os.path.exists("VRising_Manager"):
        print("📁 VRising_Manager mantido na pasta atual (correto)")
    
    # Mostrar estrutura final
    print(f"\n📋 Estrutura final:")
    print(f"📁 . (pasta atual)")
    print(f"├── 📁 VRising_Manager/")
    print(f"└── 📁 {server_folder}/")
    print(f"    ├── 📄 VRisingServer.exe")
    print(f"    ├── 📁 VRisingServer_Data/")
    print(f"    ├── 📁 save-data/")
    print(f"    ├── 📁 logs/")
    print(f"    └── 📁 backups/")
    
    return True

if __name__ == "__main__":
    try:
        success = move_server_files()
        if success:
            print("\n🎉 Arquivos movidos com sucesso!")
            print("💡 Agora execute o VRising_Manager - ele detectará automaticamente a nova estrutura")
        else:
            print("\n❌ Falha ao mover arquivos")
    except Exception as e:
        print(f"\n❌ Erro crítico: {e}")
    
    input("\nPressione Enter para sair...")
