#!/usr/bin/env python3
"""
Script para mover arquivos do VRising_Manager para a raiz e remover a pasta
"""

import os
import shutil
import sys

def move_manager_to_root():
    """Move arquivos essenciais do VRising_Manager para a raiz"""
    
    print("🔄 Movendo VRising Manager para a raiz...")
    
    # Verificar se pasta VRising_Manager existe
    if not os.path.exists("VRising_Manager"):
        print("❌ Pasta VRising_Manager não encontrada!")
        return False
    
    # Arquivos essenciais para mover
    essential_files = [
        "vrising_manager.py",
        "vrising_config.json",
        "requirements.txt",
        "run_manager.bat"
    ]
    
    # Arquivos opcionais (documentação)
    optional_files = [
        "README.md",
        "INSTALACAO.txt",
        "COMO_USAR.md",
        "GERENCIAR_JOGADORES.md",
        "SOLUCIONAR_PROBLEMAS.md"
    ]
    
    moved_files = []
    
    print("📄 Movendo arquivos essenciais...")
    
    # Mover arquivos essenciais
    for file in essential_files:
        source = os.path.join("VRising_Manager", file)
        if os.path.exists(source):
            try:
                # Se arquivo já existe na raiz, fazer backup
                if os.path.exists(file):
                    backup_name = f"{file}.backup"
                    shutil.move(file, backup_name)
                    print(f"📦 Backup criado: {backup_name}")
                
                shutil.move(source, file)
                print(f"✅ Movido: {file}")
                moved_files.append(file)
                
            except Exception as e:
                print(f"❌ Erro ao mover {file}: {e}")
        else:
            print(f"⚠️ Não encontrado: {file}")
    
    # Mover arquivos opcionais
    print("\n📚 Movendo documentação...")
    for file in optional_files:
        source = os.path.join("VRising_Manager", file)
        if os.path.exists(source):
            try:
                if os.path.exists(file):
                    os.remove(file)  # Remover versão antiga
                
                shutil.move(source, file)
                print(f"✅ Movido: {file}")
                moved_files.append(file)
                
            except Exception as e:
                print(f"❌ Erro ao mover {file}: {e}")
    
    # Remover pasta VRising_Manager
    print(f"\n🗑️ Removendo pasta VRising_Manager...")
    try:
        shutil.rmtree("VRising_Manager")
        print("✅ Pasta VRising_Manager removida")
    except Exception as e:
        print(f"❌ Erro ao remover pasta: {e}")
        print("💡 Remova manualmente se necessário")
    
    print(f"\n✅ Movidos {len(moved_files)} arquivos para a raiz")
    
    # Mostrar estrutura final
    print(f"\n📋 Estrutura final:")
    print(f"📁 . (pasta atual)")
    print(f"├── 📄 vrising_manager.py")
    print(f"├── 📄 vrising_config.json")
    print(f"├── 📄 requirements.txt")
    print(f"├── 📄 run_manager.bat")
    print(f"├── 📚 README.md")
    print(f"└── 📁 VRisingServer/")
    print(f"    ├── 📄 VRisingServer.exe")
    print(f"    ├── 📁 VRisingServer_Data/")
    print(f"    ├── 📁 save-data/")
    print(f"    ├── 📁 logs/")
    print(f"    └── 📁 backups/")
    
    return True

if __name__ == "__main__":
    try:
        success = move_manager_to_root()
        if success:
            print("\n🎉 VRising Manager movido para a raiz com sucesso!")
            print("💡 Agora execute: python vrising_manager.py")
        else:
            print("\n❌ Falha ao mover arquivos")
    except Exception as e:
        print(f"\n❌ Erro crítico: {e}")
    
    input("\nPressione Enter para sair...")
