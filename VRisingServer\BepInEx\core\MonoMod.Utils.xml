<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MonoMod.Utils</name>
    </assembly>
    <members>
        <member name="T:MonoMod.Cil.ILContext">
            <summary>
            An IL manipulation "context" with various helpers and direct access to the MethodBody.
            </summary>
        </member>
        <member name="T:MonoMod.Cil.ILContext.Manipulator">
            <summary>
            The manipulator callback, accepted by the Invoke method.
            </summary>
            <param name="il"></param>
        </member>
        <member name="P:MonoMod.Cil.ILContext.Method">
            <summary>
            The manipulated method.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.IL">
            <summary>
            The manipulated method's IL processor.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.Body">
            <summary>
            The manipulated method body.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.Module">
            <summary>
            The manipulated method's module.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.Instrs">
            <summary>
            The manipulated method instructions.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.Labels">
            <summary>
            A readonly list of all defined labels.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILContext.IsReadOnly">
            <summary>
            Has the context been made read-only? No further method access is possible, but the context has not yet been disposed.
            </summary>
        </member>
        <member name="E:MonoMod.Cil.ILContext.OnDispose">
            <summary>
            Events which run when the context will be disposed.
            </summary>
        </member>
        <member name="F:MonoMod.Cil.ILContext.ReferenceBag">
            <summary>
            The current reference bag. Used for methods such as EmitReference and EmitDelegate.
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILContext.Invoke(MonoMod.Cil.ILContext.Manipulator)">
            <summary>
            Invoke a given manipulator callback.
            </summary>
            <param name="manip">The manipulator to run in this context.</param>
        </member>
        <member name="M:MonoMod.Cil.ILContext.MakeReadOnly">
            <summary>
            Mark this ILContext as read-only and prevent this context from further accessing the originally passed method.
            </summary>
            <remarks>
            If the method is altered prior to calling MakeReadOnly or afterwards by accessing the method directly, the results are undefined.
            </remarks>
        </member>
        <member name="M:MonoMod.Cil.ILContext.Import(System.Reflection.FieldInfo)">
            <summary>
            See <see cref="M:Mono.Cecil.ModuleDefinition.ImportReference(System.Reflection.FieldInfo)"/>
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILContext.Import(System.Reflection.MethodBase)">
            <summary>
            See <see cref="M:Mono.Cecil.ModuleDefinition.ImportReference(System.Reflection.MethodBase)"/>
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILContext.Import(System.Type)">
            <summary>
            See <see cref="M:Mono.Cecil.ModuleDefinition.ImportReference(System.Type)"/>
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILContext.DefineLabel">
            <summary>
            Define a new label to be marked with a cursor.
            </summary>
            <returns>A label without a target instruction.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILContext.DefineLabel(Mono.Cecil.Cil.Instruction)">
            <summary>
            Define a new label pointing at a given instruction.
            </summary>
            <param name="target">The instruction the label will point at.</param>
            <returns>A label pointing at the given instruction.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILContext.IndexOf(Mono.Cecil.Cil.Instruction)">
            <summary>
            Determine the index of a given instruction.
            </summary>
            <param name="instr">The instruction to get the index of.</param>
            <returns>The instruction index, or the end of the method body if it hasn't been found.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILContext.GetIncomingLabels(Mono.Cecil.Cil.Instruction)">
            <summary>
            Obtain all labels pointing at the given instruction.
            </summary>
            <param name="instr">The instruction to get all labels for.</param>
            <returns>All labels targeting the given instruction.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILContext.AddReference``1(``0)">
            <summary>
            Bind an arbitary object to an ILContext for static retrieval.
            </summary>
            <typeparam name="T">The type of the object. The combination of typeparam and id provides the unique static reference.</typeparam>
            <param name="t">The object to store.</param>
            <returns>The id to use in combination with the typeparam for object retrieval.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILContext.Dispose">
            <summary>
            Dispose this context, making it read-only and invoking all OnDispose event listeners.
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILContext.ToString">
            <summary>
            Obtain a string representation of this context (method ID and body).
            </summary>
            <returns>A string representation of this context.</returns>
        </member>
        <member name="T:MonoMod.Cil.MoveType">
            <summary>
            Specifies where a ILCursor should be positioned in relation to the target of a search function
            </summary>
        </member>
        <member name="F:MonoMod.Cil.MoveType.Before">
            <summary>
             Move the cursor before the first instruction in the match
            </summary>
        </member>
        <member name="F:MonoMod.Cil.MoveType.AfterLabel">
            <summary>
            Equivalent to Before with `cursor.MoveAfterLabels()` causing emitted instructions to become the target of incoming labels
            </summary>
        </member>
        <member name="F:MonoMod.Cil.MoveType.After">
            <summary>
             Move the cursor after the last instruction in the match
            </summary>
        </member>
        <member name="T:MonoMod.Cil.SearchTarget">
            <summary>
            Indicates whether the position of a ILCursor is the result of a search function and 
            if the next search should ignore the instruction preceeding or following this cursor.
            <para />
            SearchTarget.Next is the result of searching with MoveType.Before, and SearchTarget.Prev from MoveType.After 
            </summary>
        </member>
        <member name="F:MonoMod.Cil.SearchTarget.Next">
            <summary>
            A foward searching function cannot match the Next instruction and must move the cursor forward
            </summary>
        </member>
        <member name="F:MonoMod.Cil.SearchTarget.Prev">
            <summary>
            A reverse searching function cannot match the Next instruction and must move the cursor backward
            </summary>
        </member>
        <member name="T:MonoMod.Cil.ILCursor">
            <summary>
            A cursor used to manipulate a method body in an ILContext.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Context">
            <summary>
            The context to which this cursor belongs to.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Next">
            <summary>
            The instruction immediately following the cursor position or null if the cursor is at the end of the instruction list.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Prev">
            <summary>
            The instruction immediately preceding the cursor position or null if the cursor is at the start of the instruction list.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Previous">
            <summary>
            The instruction immediately preceding the cursor position or null if the cursor is at the start of the instruction list.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Index">
            <summary>
            The index of the instruction immediately following the cursor position. Range: 0 to <c>Instrs.Count</c>
            Setter accepts negative indexing by adding <c>Instrs.Count</c> to the operand
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.SearchTarget">
            <summary>
            Indicates whether the position of a MMILCursor is the result of a search function and 
            if the next search should ignore the instruction preceeding or following this cursor.
            
            See <see cref="T:MonoMod.Cil.SearchTarget"/>
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.IncomingLabels">
            <summary>
            Enumerates all labels which point to the current instruction (<c>label.Target == Next</c>)
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Method">
            <summary>
            See <see cref="P:MonoMod.Cil.ILContext.Method"/>
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.IL">
            <summary>
            See <see cref="P:MonoMod.Cil.ILContext.IL"/>
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Body">
            <summary>
            See <see cref="P:MonoMod.Cil.ILContext.Body"/>
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Module">
            <summary>
            See <see cref="P:MonoMod.Cil.ILContext.Module"/>
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILCursor.Instrs">
            <summary>
            See <see cref="P:MonoMod.Cil.ILContext.Instrs"/>
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Clone">
            <summary>
            Create a clone of this cursor.
            </summary>
            <returns>The cloned cursor.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.IsBefore(Mono.Cecil.Cil.Instruction)">
            <summary>
            Is this cursor before the given instruction?
            </summary>
            <param name="instr">The instruction to check.</param>
            <returns>True if this cursor is before the given instruction, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.IsAfter(Mono.Cecil.Cil.Instruction)">
            <summary>
            Is this cursor after the given instruction?
            </summary>
            <param name="instr">The instruction to check.</param>
            <returns>True if this cursor is after the given instruction, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.ToString">
            <summary>
            Obtain a string representation of this cursor (method ID, index, search target, surrounding instructions).
            </summary>
            <returns>A string representation of this cursor.</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Goto(Mono.Cecil.Cil.Instruction,MonoMod.Cil.MoveType,System.Boolean)">
            <summary>
            Move the cursor to a target instruction. All other movements go through this.
            </summary>
            <param name="insn">The target instruction</param>
            <param name="moveType">Where to move in relation to the target instruction and incoming labels (branches)</param>
            <param name="setTarget">Whether to set the `SearchTarget` and skip the target instruction with the next search function</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.MoveAfterLabels">
            <summary>
            Move the cursor after incoming labels (branches). If an instruction is emitted, all labels which currently point to Next, will point to the newly emitted instruction.
            </summary>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.MoveBeforeLabels">
            <summary>
            Move the cursor before incoming labels (branches). This is the default behaviour. Emitted instructions will not cause labels to change targets.
            </summary>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Goto(System.Int32,MonoMod.Cil.MoveType,System.Boolean)">
            <summary>
            Move the cursor to a target index. Supports negative indexing. See <see cref="M:MonoMod.Cil.ILCursor.Goto(Mono.Cecil.Cil.Instruction,MonoMod.Cil.MoveType,System.Boolean)"/>
            </summary>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.GotoLabel(MonoMod.Cil.ILLabel,MonoMod.Cil.MoveType,System.Boolean)">
            <summary>
            Overload for <c>Goto(label.Target)</c>. <paramref name="moveType"/> defaults to MoveType.AfterLabel
            </summary>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.GotoNext(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search forward and moves the cursor to the next sequence of instructions matching the corresponding predicates. See also <seealso cref="M:MonoMod.Cil.ILCursor.TryGotoNext(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])"/>
            </summary>
            <returns>this</returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">If no match is found</exception>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.TryGotoNext(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search forward and moves the cursor to the next sequence of instructions matching the corresponding predicates.
            </summary>
            <returns>True if a match was found</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.GotoPrev(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search backward and moves the cursor to the next sequence of instructions matching the corresponding predicates. See also <seealso cref="M:MonoMod.Cil.ILCursor.TryGotoPrev(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])"/>
            </summary>
            <returns>this</returns>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">If no match is found</exception>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.TryGotoPrev(MonoMod.Cil.MoveType,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search backward and moves the cursor to the next sequence of instructions matching the corresponding predicates.
            </summary>
            <returns>True if a match was found</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.FindNext(MonoMod.Cil.ILCursor[]@,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Find the next occurences of a series of instructions matching the given set of predicates with gaps permitted.
            </summary>
            <param name="cursors">An array of cursors corresponding to each found instruction (MoveType.Before)</param>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">If no match is found</exception>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.TryFindNext(MonoMod.Cil.ILCursor[]@,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Find the next occurences of a series of instructions matching the given set of predicates with gaps permitted.
            </summary>
            <param name="cursors">An array of cursors corresponding to each found instruction (MoveType.Before)</param>
            <returns>True if a match was found</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.FindPrev(MonoMod.Cil.ILCursor[]@,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search backwards for occurences of a series of instructions matching the given set of predicates with gaps permitted.
            </summary>
            <param name="cursors">An array of cursors corresponding to each found instruction (MoveType.Before)</param>
            <exception cref="T:System.Collections.Generic.KeyNotFoundException">If no match is found</exception>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.TryFindPrev(MonoMod.Cil.ILCursor[]@,System.Func{Mono.Cecil.Cil.Instruction,System.Boolean}[])">
            <summary>
            Search backwards for occurences of a series of instructions matching the given set of predicates with gaps permitted.
            </summary>
            <param name="cursors">An array of cursors corresponding to each found instruction (MoveType.Before)</param>
            <returns>True if a match was found</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.MarkLabel(MonoMod.Cil.ILLabel)">
            <summary>
            Set the target of a label to the current position (<c>label.Target = Next</c>) and moves after it.
            </summary>
            <param name="label">The label to mark</param>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.MarkLabel">
            <summary>
            Create a new label targetting the current position (<c>label.Target = Next</c>) and moves after it.
            </summary>
            <returns>The newly created label</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.DefineLabel">
            <summary>
            Create a new label for use with <see cref="M:MonoMod.Cil.ILCursor.MarkLabel(MonoMod.Cil.ILLabel)"/>
            </summary>
            <returns>A new label with no target</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Remove">
            <summary>
            Remove the Next instruction
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.RemoveRange(System.Int32)">
            <summary>
            Remove several instructions
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor._Retarget(Mono.Cecil.Cil.Instruction,MonoMod.Cil.MoveType)">
            <summary>
            Move the cursor and all labels the cursor is positioned after to a target instruction
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.ParameterDefinition)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="parameter">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.Cil.VariableDefinition)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="variable">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.Cil.Instruction[])">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="targets">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.Cil.Instruction)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="target">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Double)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Single)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Int64)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.SByte)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Byte)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.String)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.FieldReference)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="field">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.CallSite)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="site">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.TypeReference)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="type">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Int32)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="value">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,Mono.Cecil.MethodReference)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="method">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Reflection.FieldInfo)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="field">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Reflection.MethodBase)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="method">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Type)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="type">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit(Mono.Cecil.Cil.OpCode,System.Object)">
            <summary>
            Emit a new instruction at this cursor's current position.
            </summary>
            <param name="opcode">The instruction opcode.</param>
            <param name="operand">The instruction operand.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.Emit``1(Mono.Cecil.Cil.OpCode,System.String)">
            <summary>
            Emit a new instruction at this cursor's current position, accessing a given member.
            </summary>
            <typeparam name="T">The type in which the member is defined.</typeparam>
            <param name="opcode">The instruction opcode.</param>
            <param name="memberName">The accessed member name.</param>
            <returns>this</returns>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.AddReference``1(``0)">
            <summary>
            Bind an arbitary object to an ILContext for static retrieval. See <see cref="M:MonoMod.Cil.ILContext.AddReference``1(``0)"/>
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.EmitGetReference``1(System.Int32)">
            <summary>
            Emit the IL to retrieve a stored reference of type <typeparamref name="T"/> with the given <paramref name="id"/> and place it on the stack.
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.EmitReference``1(``0)">
            <summary>
            Store an object in the reference store, and emit the IL to retrieve it and place it on the stack.
            </summary>
        </member>
        <member name="M:MonoMod.Cil.ILCursor.EmitDelegate``1(``0)">
            <summary>
            Emit the IL to invoke a delegate as if it were a method. Stack behaviour matches OpCodes.Call
            </summary>
        </member>
        <member name="T:MonoMod.Cil.ILLabel">
            <summary>
            A label to be used in ILContexts.
            </summary>
        </member>
        <member name="F:MonoMod.Cil.ILLabel.Target">
            <summary>
            The target instruction this label points at.
            </summary>
        </member>
        <member name="P:MonoMod.Cil.ILLabel.Branches">
            <summary>
            All instructions using this label.
            </summary>
        </member>
        <member name="T:MonoMod.Cil.IILReferenceBag">
            <summary>
            An IL inline reference bag used for ILContexts.
            </summary>
        </member>
        <member name="M:MonoMod.Cil.IILReferenceBag.Get``1(System.Int32)">
            <summary>
            Get the object for the given ID.
            </summary>
            <typeparam name="T">The object type.</typeparam>
            <param name="id">The object ID.</param>
            <returns>The stored object.</returns>
        </member>
        <member name="M:MonoMod.Cil.IILReferenceBag.GetGetter``1">
            <summary>
            Get a MethodInfo for the getter.
            </summary>
            <typeparam name="T">The object type.</typeparam>
            <returns>The getter method.</returns>
        </member>
        <member name="M:MonoMod.Cil.IILReferenceBag.Store``1(``0)">
            <summary>
            Store a new object.
            </summary>
            <typeparam name="T">The object type.</typeparam>
            <param name="t">The object to be stored.</param>
            <returns>An ID to be used for all further operations.</returns>
        </member>
        <member name="M:MonoMod.Cil.IILReferenceBag.Clear``1(System.Int32)">
            <summary>
            Remove the object with the given ID from the bag, essentially clearing the ID's slot.
            </summary>
            <typeparam name="T">The object type.</typeparam>
            <param name="id">The object ID.</param>
        </member>
        <member name="M:MonoMod.Cil.IILReferenceBag.GetDelegateInvoker``1">
            <summary>
            Get a MethodInfo invoking a delegate of the given type, with the delegate at the top of the stack. Used by <see cref="M:MonoMod.Cil.ILCursor.EmitDelegate``1(``0)"/>.
            </summary>
            <typeparam name="T">The delegate type.</typeparam>
            <returns>A MethodInfo invoking a delegate of the given type.</returns>
        </member>
        <member name="T:MonoMod.Cil.NopILReferenceBag">
            <summary>
            The default IL reference bag. Throws NotSupportedException for every operation.
            </summary>
        </member>
        <member name="T:MonoMod.Cil.RuntimeILReferenceBag">
            <summary>
            An IL reference bag implementation to be used for runtime-generated methods.
            </summary>
        </member>
        <member name="T:MonoMod.Utils.Extensions">
            <summary>
            Collection of extensions used by MonoMod and other projects.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.ToHexadecimalString(System.Byte[])">
            <summary>
            Create a hexadecimal string for the given bytes.
            </summary>
            <param name="data">The input bytes.</param>
            <returns>The output hexadecimal string.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.InvokePassing``1(System.MulticastDelegate,``0,System.Object[])">
            <summary>
            Invokes all delegates in the invocation list, passing on the result to the next.
            </summary>
            <typeparam name="T">Type of the result.</typeparam>
            <param name="md">The multicast delegate.</param>
            <param name="val">The initial value and first parameter.</param>
            <param name="args">Any other arguments that may be passed.</param>
            <returns>The result of all delegates.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.InvokeWhileTrue(System.MulticastDelegate,System.Object[])">
            <summary>
            Invokes all delegates in the invocation list, as long as the previously invoked delegate returns true.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.InvokeWhileFalse(System.MulticastDelegate,System.Object[])">
            <summary>
            Invokes all delegates in the invocation list, as long as the previously invoked delegate returns false.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.InvokeWhileNull``1(System.MulticastDelegate,System.Object[])">
            <summary>
            Invokes all delegates in the invocation list, as long as the previously invoked delegate returns null.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.SpacedPascalCase(System.String)">
            <summary>
            Split PascalCase words to become Pascal Case instead.
            </summary>
            <param name="input">PascalCaseString</param>
            <returns>Pascal Case String</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.ReadNullTerminatedString(System.IO.BinaryReader)">
            <summary>
            Read the string from the BinaryReader BinaryWriter in a C-friendly format.
            </summary>
            <param name="stream">The input which the method reads from.</param>
            <returns>The output string.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.WriteNullTerminatedString(System.IO.BinaryWriter,System.String)">
            <summary>
            Write the string to the BinaryWriter in a C-friendly format.
            </summary>
            <param name="stream">The output which the method writes to.</param>
            <param name="text">The input string.</param>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CastDelegate``1(System.Delegate)">
            <summary>
            Cast a delegate from one type to another. Compatible with delegates holding an invocation list (combined delegates).
            </summary>
            <param name="source">The input delegate.</param>
            <returns>The output delegate.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CastDelegate(System.Delegate,System.Type)">
            <summary>
            Cast a delegate from one type to another. Compatible with delegates holding an invocation list (combined delegates).
            </summary>
            <param name="source">The input delegate.</param>
            <param name="type">The wanted output delegate type.</param>
            <returns>The output delegate.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.LogDetailed(System.Exception,System.String)">
            <summary>
            Print the exception to the console, including extended loading / reflection data useful for mods.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetStateMachineTarget(System.Reflection.MethodInfo)">
            <summary>
            Get the method of interest for a given state machine method.
            </summary>
            <param name="method">The method creating the state machine.</param>
            <returns>The "main" method in the state machine.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetActualGenericMethodDefinition(System.Reflection.MethodInfo)">
            <summary>
            Gets the <i>actual</i> generic method definition of a method, as defined on the fully open type.
            </summary>
            <param name="method">The potentially instantiated method to find the definition of.</param>
            <returns>The original method definition, with no generic arguments filled in.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.SafeResolve(Mono.Cecil.TypeReference)">
            <summary>
            Safely resolve a reference, silently discarding any exceptions.
            </summary>
            <param name="r">The reference to resolve.</param>
            <returns>The resolved definition or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.SafeResolve(Mono.Cecil.FieldReference)">
            <summary>
            Safely resolve a reference, silently discarding any exceptions.
            </summary>
            <param name="r">The reference to resolve.</param>
            <returns>The resolved definition or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.SafeResolve(Mono.Cecil.MethodReference)">
            <summary>
            Safely resolve a reference, silently discarding any exceptions.
            </summary>
            <param name="r">The reference to resolve.</param>
            <returns>The resolved definition or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.SafeResolve(Mono.Cecil.PropertyReference)">
            <summary>
            Safely resolve a reference, silently discarding any exceptions.
            </summary>
            <param name="r">The reference to resolve.</param>
            <returns>The resolved definition or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetCustomAttribute(Mono.Cecil.ICustomAttributeProvider,System.String)">
            <summary>
            Get a certain custom attribute from an attribute provider.
            </summary>
            <param name="cap">The attribute provider.</param>
            <param name="attribute">The custom attribute name.</param>
            <returns>The first matching custom attribute, or null if no matching attribute has been found.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.HasCustomAttribute(Mono.Cecil.ICustomAttributeProvider,System.String)">
            <summary>
            Determine if an attribute provider has got a specific custom attribute.
            </summary>
            <param name="cap">The attribute provider.</param>
            <param name="attribute">The custom attribute name.</param>
            <returns>true if the attribute provider contains the given custom attribute, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetInt(Mono.Cecil.Cil.Instruction)">
            <summary>
            Get the integer value pushed onto the stack with this instruction.
            </summary>
            <param name="instr">The instruction to get the pushed integer value for.</param>
            <returns>The pushed integer value.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetIntOrNull(Mono.Cecil.Cil.Instruction)">
            <summary>
            Get the integer value pushed onto the stack with this instruction.
            </summary>
            <param name="instr">The instruction to get the pushed integer value for.</param>
            <returns>The pushed integer value or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.IsBaseMethodCall(Mono.Cecil.Cil.MethodBody,Mono.Cecil.MethodReference)">
            <summary>
            Determine if the method call is a base method call.
            </summary>
            <param name="body">The caller method body.</param>
            <param name="called">The called method.</param>
            <returns>True if the called method is a base method of the caller method, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.IsCallvirt(Mono.Cecil.MethodReference)">
            <summary>
            Determine if the given method can be preferably called using callvirt.
            </summary>
            <param name="method">The called method.</param>
            <returns>True if the called method can be called using callvirt, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.IsStruct(Mono.Cecil.TypeReference)">
            <summary>
            Determine if the given type is a struct (also known as "value type") or struct-alike (f.e. primitive).
            </summary>
            <param name="type">The type to check.</param>
            <returns>True if the type is a struct, primitive or similar, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.ToLongOp(Mono.Cecil.Cil.OpCode)">
            <summary>
            Get the long form opcode for any short form opcode.
            </summary>
            <param name="op">The short form opcode.</param>
            <returns>The long form opcode.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.ToShortOp(Mono.Cecil.Cil.OpCode)">
            <summary>
            Get the short form opcode for any long form opcode.
            </summary>
            <param name="op">The long form opcode.</param>
            <returns>The short form opcode.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.RecalculateILOffsets(Mono.Cecil.MethodDefinition)">
            <summary>
            Calculate updated instruction offsets. Required for certain manual fixes.
            </summary>
            <param name="method">The method to recalculate the IL instruction offsets for.</param>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FixShortLongOps(Mono.Cecil.MethodDefinition)">
            <summary>
            Fix (and optimize) any instructions which should use the long / short form opcodes instead.
            </summary>
            <param name="method">The method to apply the fixes to.</param>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Is(System.Reflection.MemberInfo,Mono.Cecil.MemberReference)">
            <summary>
            Check if the signatures of a given System.Reflection and Mono.Cecil member reference match.
            </summary>
            <param name="minfo">The System.Reflection member reference.</param>
            <param name="mref">The Mono.Cecil member reference.</param>
            <returns>True if both references share the same signature, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Is(Mono.Cecil.MemberReference,System.Reflection.MemberInfo)">
            <summary>
            Check if the signatures of a given System.Reflection and Mono.Cecil member reference match.
            </summary>
            <param name="mref">The Mono.Cecil member reference.</param>
            <param name="minfo">The System.Reflection member reference.</param>
            <returns>True if both references share the same signature, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.AddRange``1(Mono.Collections.Generic.Collection{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            See <see cref="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})"/>
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.AddRange(System.Collections.IDictionary,System.Collections.IDictionary)">
            <summary>
            See <see cref="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})"/>
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.AddRange``2(System.Collections.Generic.IDictionary{``0,``1},System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            See <see cref="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})"/>
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.AddRange``2(System.Collections.Generic.Dictionary{``0,``1},System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            See <see cref="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})"/>
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.InsertRange``1(Mono.Collections.Generic.Collection{``0},System.Int32,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            See <see cref="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})"/>
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Extensions.IsCompatible(System.Type,System.Type)">
            <summary>
            Determine if two types are compatible with each other (f.e. object with string, or enums with their underlying integer type).
            </summary>
            <param name="type">The first type.</param>
            <param name="other">The second type.</param>
            <returns>True if both types are compatible with each other, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CreateDelegate``1(System.Reflection.MethodBase)">
            <summary>
            Creates a delegate of the specified type from this method.
            </summary>
            <param name="method">The method to create the delegate from.</param>
            <typeparam name="T">The type of the delegate to create.</typeparam>
            <returns>The delegate for this method.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CreateDelegate``1(System.Reflection.MethodBase,System.Object)">
            <summary>
            Creates a delegate of the specified type with the specified target from this method.
            </summary>
            <param name="method">The method to create the delegate from.</param>
            <typeparam name="T">The type of the delegate to create.</typeparam>
            <param name="target">The object targeted by the delegate.</param>
            <returns>The delegate for this method.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CreateDelegate(System.Reflection.MethodBase,System.Type)">
            <summary>
            Creates a delegate of the specified type from this method.
            </summary>
            <param name="method">The method to create the delegate from.</param>
            <param name="delegateType">The type of the delegate to create.</param>
            <returns>The delegate for this method.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.CreateDelegate(System.Reflection.MethodBase,System.Type,System.Object)">
            <summary>
            Creates a delegate of the specified type with the specified target from this method.
            </summary>
            <param name="method">The method to create the delegate from.</param>
            <param name="delegateType">The type of the delegate to create.</param>
            <param name="target">The object targeted by the delegate.</param>
            <returns>The delegate for this method.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindMethod(Mono.Cecil.TypeDefinition,System.String,System.Boolean)">
            <summary>
            Find a method for a given ID.
            </summary>
            <param name="type">The type to search in.</param>
            <param name="id">The method ID.</param>
            <param name="simple">Whether to perform a simple search pass as well or not.</param>
            <returns>The first matching method or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindMethodDeep(Mono.Cecil.TypeDefinition,System.String,System.Boolean)">
            <summary>
            Find a method for a given ID recursively (including the passed type's base types).
            </summary>
            <param name="type">The type to search in.</param>
            <param name="id">The method ID.</param>
            <param name="simple">Whether to perform a simple search pass as well or not.</param>
            <returns>The first matching method or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindMethod(System.Type,System.String,System.Boolean)">
            <summary>
            Find a method for a given ID.
            </summary>
            <param name="type">The type to search in.</param>
            <param name="id">The method ID.</param>
            <param name="simple">Whether to perform a simple search pass as well or not.</param>
            <returns>The first matching method or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindMethodDeep(System.Type,System.String,System.Boolean)">
            <summary>
            Find a method for a given ID recursively (including the passed type's base types).
            </summary>
            <param name="type">The type to search in.</param>
            <param name="id">The method ID.</param>
            <param name="simple">Whether to perform a simple search pass as well or not.</param>
            <returns>The first matching method or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindProperty(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find a property for a given name.
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The property name.</param>
            <returns>The first matching property or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindPropertyDeep(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find a property for a given name recursively (including the passed type's base types).
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The property name.</param>
            <returns>The first matching property or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindField(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find a field for a given name.
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The field name.</param>
            <returns>The first matching field or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindFieldDeep(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find a field for a given name recursively (including the passed type's base types).
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The field name.</param>
            <returns>The first matching field or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindEvent(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find an event for a given name.
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The event name.</param>
            <returns>The first matching event or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.FindEventDeep(Mono.Cecil.TypeDefinition,System.String)">
            <summary>
            Find an event for a given name recursively (including the passed type's base types).
            </summary>
            <param name="type">The type to search in.</param>
            <param name="name">The event name.</param>
            <returns>The first matching event or null.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetID(Mono.Cecil.MethodReference,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Get a reference ID that is similar to the full name, but consistent between System.Reflection and Mono.Cecil.
            </summary>
            <param name="method">The method to get the ID for.</param>
            <param name="name">The name to use instead of the reference's own name.</param>
            <param name="type">The ID to use instead of the reference's declaring type ID.</param>
            <param name="withType">Whether the type ID should be included or not. System.Reflection avoids it by default.</param>
            <param name="simple">Whether the ID should be "simple" (name only).</param>
            <returns>The ID.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetID(Mono.Cecil.CallSite)">
            <summary>
            Get a reference ID that is similar to the full name, but consistent between System.Reflection and Mono.Cecil.
            </summary>
            <param name="method">The call site to get the ID for.</param>
            <returns>The ID.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetID(System.Reflection.MethodBase,System.String,System.String,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Get a reference ID that is similar to the full name, but consistent between System.Reflection and Mono.Cecil.
            </summary>
            <param name="method">The method to get the ID for.</param>
            <param name="name">The name to use instead of the reference's own name.</param>
            <param name="type">The ID to use instead of the reference's declaring type ID.</param>
            <param name="withType">Whether the type ID should be included or not. System.Reflection avoids it by default.</param>
            <param name="proxyMethod">Whether the method is regarded as a proxy method or not. Setting this paramater to true will skip the first parameter.</param>
            <param name="simple">Whether the ID should be "simple" (name only).</param>
            <returns>The ID.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetPatchName(Mono.Cecil.MemberReference)">
            <summary>
            Get the "patch name" - the name of the target to patch - for the given member.
            </summary>
            <param name="mr">The member to get the patch name for.</param>
            <returns>The patch name.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetPatchFullName(Mono.Cecil.MemberReference)">
            <summary>
            Get the "patch name" - the name of the target to patch - for the given member.
            </summary>
            <param name="mr">The member to get the patch name for.</param>
            <returns>The patch name.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Clone(Mono.Cecil.MethodDefinition,Mono.Cecil.MethodDefinition)">
            <summary>
            Clone the given method definition.
            </summary>
            <param name="o">The original method.</param>
            <param name="c">The method definition to apply the cloning process onto, or null to create a new method.</param>
            <returns>A clone of the original method.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Clone(Mono.Cecil.Cil.MethodBody,Mono.Cecil.MethodDefinition)">
            <summary>
            Clone the given method body.
            </summary>
            <param name="bo">The original method body.</param>
            <param name="m">The method which will own the newly cloned method body.</param>
            <returns>A clone of the original method body.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Update(Mono.Cecil.GenericParameter,System.Int32,Mono.Cecil.GenericParameterType)">
            <summary>
            Force-update a generic parameter's position and type.
            </summary>
            <param name="param">The generic parameter to update.</param>
            <param name="position">The new position.</param>
            <param name="type">The new type.</param>
            <returns>The updated generic parameter.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.ResolveGenericParameter(Mono.Cecil.IGenericParameterProvider,Mono.Cecil.GenericParameter)">
            <summary>
            Resolve a given generic parameter in another context.
            </summary>
            <param name="provider">The new context.</param>
            <param name="orig">The original generic parameter.</param>
            <returns>A generic parameter provided by the given context which matches the original generic parameter.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.IMetadataTokenProvider,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given member reference (metadata token provider).
            </summary>
            <param name="mtp">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.TypeReference,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given type reference.
            </summary>
            <param name="type">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.MethodReference,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given method reference.
            </summary>
            <param name="method">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.CallSite,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given callsite.
            </summary>
            <param name="method">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.FieldReference,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given field reference.
            </summary>
            <param name="field">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.ParameterDefinition,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given parameter definition.
            </summary>
            <param name="param">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Clone(Mono.Cecil.ParameterDefinition)">
            <summary>
            Clone the given parameter definition.
            </summary>
            <param name="param">The original parameter definition.</param>
            <returns>A clone of the original parameter definition.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.CustomAttribute,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given custom attribute.
            </summary>
            <param name="attrib">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Clone(Mono.Cecil.CustomAttribute)">
            <summary>
            Clone the given custom attribute.
            </summary>
            <param name="attrib">The original custom attribute.</param>
            <returns>A clone of the original custom attribute.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Relink(Mono.Cecil.GenericParameter,MonoMod.Utils.Relinker,Mono.Cecil.IGenericParameterProvider)">
            <summary>
            Relink the given generic parameter reference.
            </summary>
            <param name="param">The reference to relink.</param>
            <param name="relinker">The relinker to use during the relinking process.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.Clone(Mono.Cecil.GenericParameter)">
            <summary>
            Clone the given generic parameter.
            </summary>
            <param name="param">The original generic parameter.</param>
            <returns>A clone of the original generic parameter.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetManagedSize(System.Type)">
            <summary>
            Get the managed size of a given type. This matches an IL-level sizeof(t), even if it cannot be determined normally in C#.
            Note that sizeof(t) != Marshal.SizeOf(t), f.e. when t is char.
            </summary>
            <param name="t">The type to get the size from.</param>
            <returns>The managed type size.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetThisParamType(System.Reflection.MethodBase)">
            <summary>
            Get a type which matches what the method should receive via ldarg.0
            </summary>
            <param name="method">The method to obtain the "this" parameter type from.</param>
            <returns>The "this" parameter type.</returns>
        </member>
        <member name="M:MonoMod.Utils.Extensions.GetLdftnPointer(System.Reflection.MethodBase)">
            <summary>
            Get a native function pointer for a given method. This matches an IL-level ldftn.
            </summary>
            <remarks>
            The result of ldftn doesn't always match that of MethodHandle.GetFunctionPointer().
            For example, ldftn doesn't JIT-compile the method on mono, which thus keeps the class constructor untouched.
            And on .NET, struct overrides (f.e. ToString) have got multiple entry points pointing towards the same code.
            </remarks>
            <param name="m">The method to get a native function pointer for.</param>
            <returns>The native function pointer.</returns>
        </member>
        <member name="T:MonoMod.Utils.Cil.CecilILGenerator">
            <summary>
            A variant of ILGenerator which uses Mono.Cecil under the hood.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Cil.CecilILGenerator.IL">
            <summary>
            The underlying Mono.Cecil.Cil.ILProcessor.
            </summary>
        </member>
        <member name="T:MonoMod.Utils.Cil.ILGeneratorShim">
            <summary>
            Abstract version of System.Reflection.Emit.ILGenerator. See <see cref="T:System.Reflection.Emit.ILGenerator"/> for proper documentation.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.Cil.ILGeneratorShim.GetProxy">
            <summary>
            Get a "real" ILGenerator for this ILGeneratorShim.
            </summary>
            <returns>A "real" ILGenerator.</returns>
        </member>
        <member name="M:MonoMod.Utils.Cil.ILGeneratorShim.GetProxyType``1">
            <summary>
            Get the proxy type for a given ILGeneratorShim type. The proxy type implements ILGenerator.
            </summary>
            <typeparam name="TShim">The ILGeneratorShim type.</typeparam>
            <returns>The "real" ILGenerator type.</returns>
        </member>
        <member name="M:MonoMod.Utils.Cil.ILGeneratorShim.GetProxyType(System.Type)">
            <summary>
            Get the proxy type for a given ILGeneratorShim type. The proxy type implements ILGenerator.
            </summary>
            <param name="tShim">The ILGeneratorShim type.</param>
            <returns>The "real" ILGenerator type.</returns>
        </member>
        <member name="P:MonoMod.Utils.Cil.ILGeneratorShim.ProxyType">
            <summary>
            Get the non-generic proxy type implementing ILGenerator.
            </summary>
            <returns>The "real" ILGenerator type, non-generic.</returns>
        </member>
        <member name="T:MonoMod.Utils.DMDGenerator`1">
            <summary>
            A DynamicMethodDefinition "generator", responsible for generating a runtime MethodInfo from a DMD MethodDefinition.
            </summary>
            <typeparam name="TSelf"></typeparam>
        </member>
        <member name="T:MonoMod.Utils.DMDCecilGenerator">
            <summary>
            A DMDGenerator implementation using Mono.Cecil to build an in-memory assembly.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.Stub(System.Reflection.Emit.DynamicMethod)">
            <summary>
            Fill the DynamicMethod with a stub.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.Stub(MonoMod.Utils.DynamicMethodDefinition)">
            <summary>
            Fill the DynamicMethod with a stub.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.EmitReference``1(System.Reflection.Emit.ILGenerator,``0)">
            <summary>
            Emit a reference to an arbitrary object. Note that the references "leak."
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.EmitReference``1(Mono.Cecil.Cil.ILProcessor,``0)">
            <summary>
            Emit a reference to an arbitrary object. Note that the references "leak."
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.EmitGetReference``1(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Emit a reference to an arbitrary object. Note that the references "leak."
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynamicMethodHelper.EmitGetReference``1(Mono.Cecil.Cil.ILProcessor,System.Int32)">
            <summary>
            Emit a reference to an arbitrary object. Note that the references "leak."
            </summary>
        </member>
        <member name="F:MonoMod.Utils.DynDll.Mappings">
            <summary>
            Allows you to remap library paths / names and specify loading flags. Useful for cross-platform compatibility. Applies only to DynDll.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynDll.OpenLibrary(System.String,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Open a given library and get its handle.
            </summary>
            <param name="name">The library name.</param>
            <param name="skipMapping">Whether to skip using the mapping or not.</param>
            <param name="flags">Any optional platform-specific flags.</param>
            <returns>The library handle.</returns>
        </member>
        <member name="M:MonoMod.Utils.DynDll.TryOpenLibrary(System.String,System.IntPtr@,System.Boolean,System.Nullable{System.Int32})">
            <summary>
            Try to open a given library and get its handle.
            </summary>
            <param name="name">The library name.</param>
            <param name="libraryPtr">The library handle, or null if it failed loading.</param>
            <param name="skipMapping">Whether to skip using the mapping or not.</param>
            <param name="flags">Any optional platform-specific flags.</param>
            <returns>True if the handle was obtained, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.DynDll.CloseLibrary(System.IntPtr)">
            <summary>
            Release a library handle obtained via OpenLibrary. Don't release the result of OpenLibrary(null)!
            </summary>
            <param name="lib">The library handle.</param>
        </member>
        <member name="M:MonoMod.Utils.DynDll.GetFunction(System.IntPtr,System.String)">
            <summary>
            Get a function pointer for a function in the given library.
            </summary>
            <param name="libraryPtr">The library handle.</param>
            <param name="name">The function name.</param>
            <returns>The function pointer.</returns>
        </member>
        <member name="M:MonoMod.Utils.DynDll.TryGetFunction(System.IntPtr,System.String,System.IntPtr@)">
            <summary>
            Get a function pointer for a function in the given library.
            </summary>
            <param name="libraryPtr">The library handle.</param>
            <param name="name">The function name.</param>
            <param name="functionPtr">The function pointer, or null if it wasn't found.</param>
            <returns>True if the function pointer was obtained, false otherwise.</returns>
        </member>
        <member name="M:MonoMod.Utils.DynDll.AsDelegate``1(System.IntPtr)">
            <summary>
            Extension method wrapping Marshal.GetDelegateForFunctionPointer
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynDll.ResolveDynDllImports(System.Type,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{MonoMod.Utils.DynDllMapping}})">
            <summary>
            Fill all static delegate fields with the DynDllImport attribute.
            Call this early on in the static constructor.
            </summary>
            <param name="type">The type containing the DynDllImport delegate fields.</param>
            <param name="mappings">Any optional mappings similar to the static mappings.</param>
        </member>
        <member name="M:MonoMod.Utils.DynDll.ResolveDynDllImports(System.Object,System.Collections.Generic.Dictionary{System.String,System.Collections.Generic.List{MonoMod.Utils.DynDllMapping}})">
            <summary>
            Fill all instance delegate fields with the DynDllImport attribute.
            Call this early on in the constructor.
            </summary>
            <param name="instance">An instance of a type containing the DynDllImport delegate fields.</param>
            <param name="mappings">Any optional mappings similar to the static mappings.</param>
        </member>
        <member name="T:MonoMod.Utils.DynDllImportAttribute">
            <summary>
            Similar to DllImport, but requires you to run typeof(DeclaringType).ResolveDynDllImports();
            </summary>
        </member>
        <member name="P:MonoMod.Utils.DynDllImportAttribute.LibraryName">
            <summary>
            The library or library alias to use.
            </summary>
        </member>
        <member name="P:MonoMod.Utils.DynDllImportAttribute.EntryPoints">
            <summary>
            A list of possible entrypoints that the function can be resolved to. Implicitly includes the field name and delegate name.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynDllImportAttribute.#ctor(System.String,System.String[])">
            <param name="libraryName">The library or library alias to use.</param>
            <param name="entryPoints">A list of possible entrypoints that the function can be resolved to. Implicitly includes the field name and delegate name.</param>
        </member>
        <member name="T:MonoMod.Utils.DynDllMapping">
            <summary>
            A mapping entry, to be used by <see cref="T:MonoMod.Utils.DynDllImportAttribute"/>.
            </summary>
        </member>
        <member name="P:MonoMod.Utils.DynDllMapping.LibraryName">
            <summary>
            The name as which the library will be resolved as. Useful to remap libraries or to provide full paths.
            </summary>
        </member>
        <member name="P:MonoMod.Utils.DynDllMapping.Flags">
            <summary>
            Platform-dependent loading flags.
            </summary>
        </member>
        <member name="M:MonoMod.Utils.DynDllMapping.#ctor(System.String,System.Nullable{System.Int32})">
            <param name="libraryName">The name as which the library will be resolved as. Useful to remap libraries or to provide full paths.</param>
            <param name="flags">Platform-dependent loading flags.</param>
        </member>
        <member name="T:MonoMod.Utils.Relinker">
            <summary>
            The relinker callback delegate type.
            </summary>
            <param name="mtp">The reference (metadata token provider) to relink.</param>
            <param name="context">The generic context provided to relink generic references.</param>
            <returns>A relinked reference.</returns>
        </member>
        <member name="T:MonoMod.Utils.Platform">
            <summary>
            Generic platform enum.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.OS">
            <summary>
            Bit applied to all OSes (Unknown, Windows, MacOS, ...). 
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Bits64">
            <summary>
            On demand 64-bit platform bit.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.NT">
            <summary>
            Applied to all NT and NT-oid platforms (Windows).
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Unix">
            <summary>
            Applied to all Unix and Unix-oid platforms (macOS, Linux, ...).
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.ARM">
            <summary>
            On demand ARM platform bit.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Wine">
            <summary>
            On demand Wine bit. DON'T RELY ON THIS.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Unknown">
            <summary>
            Unknown OS.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Windows">
            <summary>
            Windows, using the NT kernel.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.MacOS">
            <summary>
            macOS, using the Darwin kernel.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Linux">
            <summary>
            Linux.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.Android">
            <summary>
            Android, using the Linux kernel.
            </summary>
        </member>
        <member name="F:MonoMod.Utils.Platform.iOS">
            <summary>
            iOS, sharing components with macOS.
            </summary>
        </member>
    </members>
</doc>
