{"root": [{"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "MagicaPhysicsManager", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "MagicaPhysicsManager", "methodName": "InitCustomGameLoop", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "MagicaCloth", "nameSpace": "MagicaCloth", "className": "SimpleInputManager", "methodName": "Init", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "MagicaCloth", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3018880898", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Behaviours", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3622276133", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Behaviours", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3622276133", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Behaviours", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Camera", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2868561727", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Camera", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.CastleBuilding.Systems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3141550756", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.CastleBuilding.Systems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3141550756", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.CastleBuilding.Systems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.CastleDyeSystem", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__308143012", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Conversion", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__799101055", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Conversion", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__799101055", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Conversion", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__2889183797", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2889183797", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Gameplay.Scripting", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__293644288", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Gameplay.Scripting", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Gameplay.Systems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__2815404940", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Gameplay.Systems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2815404940", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Gameplay.Systems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.GeneratedNetCode", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1475307704", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.GeneratedNetCode", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1475307704", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.GeneratedNetCode", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Haptics", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3656262727", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Haptics", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3656262727", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Haptics", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.HUD", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__982077580", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.HUD", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__982077580", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.HUD", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Misc.Systems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1782478695", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Misc.Systems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1782478695", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Misc.Systems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Pathfinding", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__207358377", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Pathfinding", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Presentation.Systems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__684380587", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Presentation.Systems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__684380587", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Presentation.Systems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.ScriptableSystems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3997913284", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.ScriptableSystems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3997913284", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.ScriptableSystems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared", "nameSpace": "ProjectM", "className": "AutomaticWorldBootstrap", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared", "nameSpace": "ProjectM", "className": "TurboInteraction", "methodName": "RegisterInteraction", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1489081747", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1489081747", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared.Systems", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1136929487", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared.Systems", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1136929487", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Shared.Systems", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Terrain", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__625989149", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Terrain", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__625989149", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Terrain", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Wind.Shared", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3612758143", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ProjectM.Wind.Shared", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "RootMotion", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Hybrid", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__2190553623", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Hybrid", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2190553623", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Runtime", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3418879652", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3418879652", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Rukhanka.Toolbox", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Animation.StunCloth.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3075231270", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Animation.StunCloth.Runtime", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core.Animation", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__151907223", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core.Animation", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__151907223", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core.Animation", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1280732401", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1280732401", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Core", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Stunlock.Fmod", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2451905671", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__244505174", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "Unity.Entities", "className": "DefaultWorldInitialization", "methodName": "CleanupWorldBeforeSceneLoad", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "Unity.Entities.Content", "className": "RuntimeContentManager", "methodName": "RuntimeInitialization", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "Unity.Entities.Content", "className": "RuntimeContentSystem", "methodName": "RuntimeInitialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__2599647964", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2599647964", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Graphics", "nameSpace": "Unity.Rendering", "className": "CallFromBurstRenderMeshArrayHelper", "methodName": "RuntimeInitialization", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Graphics", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1331641335", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Graphics", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1331641335", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Graphics", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Hybrid", "nameSpace": "Unity.Entities", "className": "AttachToEntityClonerInjection", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Hybrid", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2393600038", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Entities.Hybrid", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Mathematics.Extensions", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__673277649", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.MemoryProfiler", "nameSpace": "Unity.MemoryProfiler", "className": "MetadataInjector", "methodName": "PlayerInitMetadata", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.Physics", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3553254632", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Physics", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3553254632", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Physics", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Physics.Hybrid", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3893159723", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Physics.Hybrid", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3893159723", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Physics.Hybrid", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "HDRuntimeReflectionSystem", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1362632656", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Scenes", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__1944377162", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Scenes", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1944377162", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Scenes", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization", "className": "DefaultPropertyBagInitializer", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization.Json", "className": "JsonObject", "methodName": "RegisterPropertyBag", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "Unity.Serialization.Json", "className": "JsonArray", "methodName": "RegisterPropertyBag", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Serialization", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__121624576", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core", "nameSpace": "Unity.Services.Core", "className": "UnityThreadUtils", "methodName": "CaptureUnityThreadInfo", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "TaskAsyncOperation", "methodName": "SetScheduler", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "CreateStaticInstance", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Internal", "nameSpace": "Unity.Services.Core.Internal", "className": "UnityServicesInitializer", "methodName": "EnableServicesInitializationAsync", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.Services.Core.Registration", "nameSpace": "Unity.Services.Core.Registration", "className": "CorePackageInitializer", "methodName": "InitializeOnLoad", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Services.Vivox", "nameSpace": "Unity.Services.Vivox", "className": "VivoxPackageInitializer", "methodName": "Register", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.Transforms", "nameSpace": "", "className": "__UnmanagedPostProcessorOutput__3287167136", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Transforms", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3287167136", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Transforms", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Transforms.Hybrid", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2299039643", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Transforms.Hybrid", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}]}