{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "com.rlabrecque.steamworks.net.dll", "ProjectM.Camera.dll", "ProjectM.Hybrid.Performance.dll", "ProjectM.CastleDyeSystem.dll", "ProjectM.DualsensePC.dll", "Malee.ReorderableList.dll", "ProjectM.Haptics.dll", "Unity.ScriptableBuildPipeline.dll", "Unity.Services.Core.dll", "com.stunlock.platform.pc.dll", "ProjectM.Conversion.dll", "Stunlock.Network.EosSdk.dll", "Stunlock.Network.dll", "Stunlock.ContentTests.dll", "RTLTMPro.dll", "ProjectM.GeneratedNetCode.dll", "ProjectM.Steam.dll", "Unity.InternalAPIEngineBridge.002.dll", "Unity.Services.Core.Scheduler.dll", "Unity.Services.Core.Configuration.dll", "Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "RCONServerLib.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "ProjectM.LiveBuildPerformanceTests.GlowingVampire.dll", "Unity.Entities.Hybrid.HybridComponents.dll", "Unity.Collections.dll", "ProjectM.CodeGeneration.dll", "Unity.Deformations.dll", "Unity.TextMeshPro.dll", "ProjectM.HUD.dll", "Unity.Mathematics.Extensions.Hybrid.dll", "MagicaCloth.dll", "ProjectM.Editor.SampleHandler.dll", "Unity.MemoryProfiler.dll", "Unity.Burst.dll", "Stunlock.Console.dll", "Unity.Services.Core.Device.dll", "Unity.Services.Core.Threading.dll", "Stunlock.Core.dll", "ProjectM.Behaviours.dll", "ProjectM.Gameplay.Scripting.dll", "Unity.InputSystem.ForUI.dll", "Unity.Services.Core.Components.dll", "Unity.Transforms.dll", "Unity.Serialization.dll", "Rukhanka.DebugDrawer.dll", "ProjectM.ScriptableSystems.dll", "ProjectM.LiveBuildPerformanceTests.Utilities.dll", "Lidgren.Network.dll", "Unity.Entities.UI.dll", "ProjectM.Shared.ColorData.dll", "ProjectM.VFXRendererUtility.dll", "Unity.Scenes.dll", "com.stunlock.platform.dll", "Stunlock.Fmod.dll", "Stunlock.Network.Lidgren.dll", "Unity.VisualEffectGraph.Runtime.dll", "Stunlock.Animation.StunCloth.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "ProjectM.PerformanceTests.Editor.dll", "Unity.Services.Vivox.dll", "VivoxUnity.dll", "Unity.Services.Core.Environments.Internal.dll", "ProjectM.Wind.Shared.dll", "Unity.Entities.dll", "RootMotion.dll", "Unity.Services.Core.Networking.dll", "ProjectM.Pathfinding.dll", "Unity.Entities.Hybrid.dll", "ProjectM.Roofs.dll", "Rukhanka.Hybrid.dll", "Rukhanka.Runtime.dll", "Stunlock.Tools.dll", "Unity.Services.Core.Environments.dll", "Unity.Physics.dll", "Stunlock.Core.Animation.dll", "ProjectM.Gameplay.Systems.dll", "Unity.Physics.Hybrid.dll", "Unity.Services.Core.Analytics.dll", "ProjectM.Shared.Systems.dll", "Stunlock.Metrics.dll", "com.stunlock.network.eos.dll", "Unity.Profiling.Core.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Sequencer.dll", "ProjectM.dll", "Rukhanka.Toolbox.dll", "ProjectM.Presentation.Systems.dll", "Unity.Services.Core.Internal.dll", "Stunlock.Network.Steam.dll", "UnityEngine.UI.dll", "Unity.Services.Core.Telemetry.dll", "Unity.Entities.Graphics.dll", "ProjectM.Misc.Systems.dll", "Unity.Transforms.Hybrid.dll", "Unity.InputSystem.dll", "com.stunlock.network.localonly.dll", "Backtrace.Unity.dll", "Stunlock.Localization.dll", "ProjectM.Shared.dll", "Unity.Mathematics.dll", "Unity.Mathematics.Extensions.dll", "Unity.Services.Core.Registration.dll", "ProjectM.CastleBuilding.Systems.dll", "Stunlock.Network.Steam.Demo.dll", "Unity.RenderPipelines.HighDefinition.Runtime.dll", "ProjectM.Terrain.dll", "Mono.Data.Sqlite.dll", "Unity.Collections.LowLevel.ILSupport.dll", "System.Runtime.CompilerServices.Unsafe.dll", "Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll", "System.Collections.Immutable.dll", "System.Reflection.Metadata.dll", "Unity.Burst.Unsafe.dll", "Microsoft.CodeAnalysis.CSharp.dll", "Newtonsoft.Json.dll", "Microsoft.CodeAnalysis.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}