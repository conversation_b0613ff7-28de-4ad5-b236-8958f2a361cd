@echo off
title V Rising Server Manager

echo ==========================================
echo V Rising Server Manager
echo ==========================================
echo.

REM Verificar Python
echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python nao encontrado!
    echo Execute instalar.bat primeiro.
    echo.
    pause
    exit /b 1
)

REM Verificar dependencias
echo Verificando dependencias...
python -c "import customtkinter, psutil" >nul 2>&1
if errorlevel 1 (
    echo ERRO: Dependencias nao instaladas!
    echo Execute instalar.bat primeiro.
    echo.
    pause
    exit /b 1
)

REM Verificar se esta na pasta correta
if not exist "..\VRisingServer.exe" (
    echo AVISO: VRisingServer.exe nao encontrado na pasta pai.
    echo Certifique-se de que esta pasta esta no diretorio do servidor.
    echo.
    echo Pressione qualquer tecla para continuar mesmo assim...
    pause >nul
)

echo Iniciando V Rising Server Manager...
echo.
python vrising_manager.py

if errorlevel 1 (
    echo.
    echo Erro ao executar o gerenciador!
    echo Verifique se todos os arquivos estao presentes.
    echo.
    pause
)
