@echo off
chcp 65001 >nul
title V Rising Server Manager

echo 🧛 V Rising Server Manager
echo ========================
echo.

REM Verificar Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado!
    echo Execute install.bat primeiro.
    pause
    exit /b 1
)

REM Verificar dependências
python -c "import customtkinter, psutil" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Dependências não instaladas!
    echo Execute install.bat primeiro.
    pause
    exit /b 1
)

echo 🚀 Iniciando V Rising Server Manager...
echo.
python vrising_manager.py

if errorlevel 1 (
    echo.
    echo ❌ Erro ao executar o gerenciador!
    echo Verifique se todos os arquivos estão presentes.
    pause
)
