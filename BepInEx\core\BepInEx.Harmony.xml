<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BepInEx.Harmony</name>
    </assembly>
    <members>
        <member name="T:BepInEx.Harmony.ParameterByRefAttribute">
            <summary>
            Specifies the indices of parameters that are ByRef.
            </summary>
        </member>
        <member name="P:BepInEx.Harmony.ParameterByRefAttribute.ParameterIndices">
            <summary>
            The indices of parameters that are ByRef.
            </summary>
        </member>
        <member name="M:BepInEx.Harmony.ParameterByRefAttribute.#ctor(System.Int32[])">
            <param name="parameterIndices">The indices of parameters that are ByRef.</param>
        </member>
        <member name="T:BepInEx.Harmony.HarmonyExtensions">
            <summary>
            An extension class for Harmony based operations.
            </summary>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyExtensions.PatchAll(HarmonyLib.Harmony,System.Type)">
            <summary>
            Applies all patches specified in the type.
            </summary>
            <param name="harmonyInstance">The HarmonyInstance to use.</param>
            <param name="type">The type to scan.</param>
        </member>
        <member name="T:BepInEx.Harmony.HarmonyWrapper">
            <summary>
            A wrapper for Harmony based operations.
            </summary>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(System.Type,HarmonyLib.Harmony)">
            <summary>
            Applies all patches specified in the type.
            </summary>
            <param name="type">The type to scan.</param>
            <param name="harmonyInstance">The HarmonyInstance to use.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(System.Type,System.String)">
            <summary>
            Applies all patches specified in the type.
            </summary>
            <param name="type">The type to scan.</param>
            <param name="harmonyInstanceId">The ID for the Harmony instance to create, which will be used.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(System.Reflection.Assembly,HarmonyLib.Harmony)">
            <summary>
            Applies all patches specified in the assembly.
            </summary>
            <param name="assembly">The assembly to scan.</param>
            <param name="harmonyInstance">The HarmonyInstance to use.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(System.Reflection.Assembly,System.String)">
            <summary>
            Applies all patches specified in the assembly.
            </summary>
            <param name="assembly">The assembly to scan.</param>
            <param name="harmonyInstanceId">The ID for the Harmony instance to create, which will be used.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(HarmonyLib.Harmony)">
            <summary>
            Applies all patches specified in the calling assembly.
            </summary>
            <param name="harmonyInstance">The Harmony instance to use.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.PatchAll(System.String)">
            <summary>
            Applies all patches specified in the calling assembly.
            </summary>
            <param name="harmonyInstanceId">The ID for the Harmony instance to create, which will be used.</param>
        </member>
        <member name="M:BepInEx.Harmony.HarmonyWrapper.EmitDelegate``1(``0)">
            <summary>
            Returns an instruction to call the specified delegate.
            </summary>
            <typeparam name="T">The delegate type to emit.</typeparam>
            <param name="action">The delegate to emit.</param>
            <returns>The instruction to </returns>
        </member>
    </members>
</doc>
