@echo off
echo ========================================
echo V Rising Server Manager - Instalacao
echo ========================================
echo.

REM Verificar se Python esta instalado
echo Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERRO: Python nao encontrado!
    echo.
    echo Para instalar Python:
    echo 1. Acesse: https://python.org/downloads/
    echo 2. Baixe Python 3.8 ou superior
    echo 3. Durante a instalacao, marque "Add Python to PATH"
    echo 4. Execute este script novamente
    echo.
    echo Alternativa: Instale pelo Microsoft Store
    echo    Digite "python" no menu iniciar e clique em "Obter"
    echo.
    pause
    exit /b 1
)

echo Python encontrado!
python --version
echo.

REM Verificar se pip esta disponivel
echo Verificando pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: pip nao encontrado!
    echo Reinstale Python com pip incluido.
    pause
    exit /b 1
)

echo pip encontrado!
echo.

REM Instalar dependencias
echo Instalando dependencias...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo Falha na instalacao padrao. Tentando com --user...
    pip install --user -r requirements.txt

    if errorlevel 1 (
        echo.
        echo ERRO: Nao foi possivel instalar as dependencias!
        echo.
        echo Tente manualmente:
        echo pip install customtkinter psutil
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Instalacao concluida com sucesso!
echo ========================================
echo.
echo Para executar o gerenciador:
echo    python vrising_manager.py
echo.
echo Leia o README_MANAGER.md para mais informacoes
echo.
pause
