# 🧛 V Rising Server Manager

Interface moderna em Python para gerenciar servidor V Rising.

## 📁 Arquivos Inclusos

- **`vrising_manager.py`** - Aplicação principal
- **`instalar.bat`** - Instala dependências Python
- **`run_manager.bat`** - Executa o manager
- **`requirements.txt`** - Lista de dependências
- **`verificar_sistema.bat`** - Diagnóstico do sistema
- **`README_MANAGER.md`** - Documentação completa
- **`COMO_USAR.md`** - Guia passo-a-passo
- **`vrising_config_example.json`** - Exemplo de configuração

## 🚀 Instalação Rápida

### 1. Copiar Arquivos
Copie toda esta pasta `VRising_Manager` para o diretório do seu servidor V Rising:
```
VRisingServer/
├── VRisingServer.exe          # ← Seu servidor
├── VRising_Manager/           # ← Esta pasta
│   ├── vrising_manager.py
│   ├── instalar.bat
│   └── ...
└── save-data/
```

### 2. Instalar Dependências
1. Entre na pasta `VRising_Manager`
2. Execute `instalar.bat`
3. Aguarde a instalação

### 3. Executar Manager
1. Execute `run_manager.bat`
2. Ou: `python vrising_manager.py`

## ⚙️ Funcionalidades

- ✅ **Controle do Servidor**: Iniciar/Parar/Reiniciar
- ✅ **Monitoramento**: CPU, RAM, Uptime em tempo real
- ✅ **Backups**: Automáticos e manuais
- ✅ **Configurações**: Interface gráfica para todas as opções
- ✅ **Logs**: Visualização em tempo real
- ✅ **Interface Moderna**: Design escuro e intuitivo

## 🔧 Requisitos

- **Python 3.8+** instalado
- **V Rising Dedicated Server**
- **Windows 10/11** (testado)

## 📞 Problemas?

1. **Python não encontrado**: Instale de https://python.org
2. **Dependências faltando**: Execute `instalar.bat`
3. **Servidor não inicia**: Verifique se está na pasta correta

## 📖 Documentação

- **`COMO_USAR.md`** - Guia completo passo-a-passo
- **`README_MANAGER.md`** - Documentação técnica detalhada

---

**Desenvolvido para a comunidade V Rising** 🧛‍♂️
