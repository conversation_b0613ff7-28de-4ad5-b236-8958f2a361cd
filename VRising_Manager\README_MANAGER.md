# 🧛 V Rising Server Manager

Interface moderna em Python para gerenciar seu servidor V Rising de forma fácil e intuitiva.

## 🚀 Características

- **Interface Moderna**: Design escuro e intuitivo usando CustomTkinter
- **Controle Completo**: Iniciar, parar e reiniciar o servidor com um clique
- **Monitoramento em Tempo Real**: CPU, RAM, uptime e logs ao vivo
- **Sistema de Backup**: Backups automáticos e manuais dos saves
- **Configuração Fácil**: Interface gráfica para todas as configurações
- **Logs Integrados**: Visualização de logs em tempo real
- **Multiplataforma**: Funciona no Windows, Linux e macOS

## 📋 Requisitos

- Python 3.8 ou superior
- V Rising Dedicated Server
- Windows 10/11 (testado) ou Linux/macOS

## 🔧 Instalação

### Método Automático (Windows)
1. Execute `install.bat` como administrador
2. Aguarde a instalação das dependências
3. Execute `python vrising_manager.py`

### Método Manual
1. Instale as dependências:
```bash
pip install -r requirements.txt
```

2. Execute o gerenciador:
```bash
python vrising_manager.py
```

## 📖 Como Usar

### 1. Primeira Execução
- Abra o gerenciador
- Vá para a aba "⚙️ Configurações"
- Configure o nome do servidor, porta, etc.
- Clique em "💾 Salvar Configurações"

### 2. Iniciando o Servidor
- Na tela principal, clique em "▶️ Iniciar"
- O servidor será iniciado automaticamente
- Acompanhe os logs na aba "📊 Dashboard"

### 3. Gerenciando Backups
- Vá para a aba "💾 Backups"
- Clique em "📦 Criar Backup" para backup manual
- Backups automáticos são criados ao iniciar o servidor

### 4. Monitoramento
- A aba "📊 Dashboard" mostra estatísticas em tempo real
- CPU, RAM e uptime são atualizados automaticamente
- Console mostra logs do servidor ao vivo

## ⚙️ Configurações Disponíveis

- **Nome do Servidor**: Nome que aparece na lista de servidores
- **Nome do Save**: Nome do arquivo de save
- **Porta**: Porta do servidor (padrão: 9876)
- **Máximo de Jogadores**: Limite de jogadores simultâneos
- **Backup Automático**: Criar backup ao iniciar servidor

## 📁 Estrutura de Arquivos

```
VRisingServer/
├── vrising_manager.py      # Gerenciador principal
├── vrising_config.json     # Configurações salvas
├── requirements.txt        # Dependências Python
├── install.bat            # Script de instalação
├── save-data/             # Dados do servidor
├── logs/                  # Logs do servidor
└── backups/               # Backups automáticos
```

## 🔧 Funcionalidades

### ✅ Implementadas
- [x] Interface principal moderna
- [x] Controle do servidor (iniciar/parar/reiniciar)
- [x] Monitoramento de recursos (CPU/RAM)
- [x] Sistema de backup automático
- [x] Visualização de logs
- [x] Configurações personalizáveis
- [x] Console em tempo real

### 🚧 Em Desenvolvimento
- [ ] Gerenciamento de jogadores (ban/unban/admin)
- [ ] Restauração de backups via interface
- [ ] Agendamento de reinicializações
- [ ] Notificações por Discord/email
- [ ] Estatísticas avançadas
- [ ] Editor de configurações de jogo

## 🐛 Solução de Problemas

### Servidor não inicia
1. Verifique se o `VRisingServer.exe` está no diretório correto
2. Confirme se a porta não está em uso
3. Verifique os logs na aba "📋 Logs"

### Erro de dependências
1. Execute `install.bat` como administrador
2. Ou instale manualmente: `pip install customtkinter psutil`

### Interface não aparece
1. Verifique se Python 3.8+ está instalado
2. Tente executar: `python -m tkinter` para testar Tkinter

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs na aba "📋 Logs"
2. Consulte o console do Python para erros
3. Verifique se todas as dependências estão instaladas

## 🎮 Compatibilidade

- ✅ V Rising Dedicated Server (todas as versões)
- ✅ Windows 10/11
- ✅ Python 3.8+
- 🔄 Linux/macOS (em teste)

## 📝 Changelog

### v1.0.0
- Interface principal com abas
- Controle básico do servidor
- Sistema de backup
- Monitoramento de recursos
- Configurações personalizáveis

---

**Desenvolvido para a comunidade V Rising** 🧛‍♂️
