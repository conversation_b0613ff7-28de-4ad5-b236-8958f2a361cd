@echo off
setlocal ENABLEEXTENSIONS

REM ================================
REM V RISING DEDICATED SERVER START
REM ================================

REM ANSI ESC para cor (Windows 10+)
for /f "delims=" %%i in ('"prompt $E"') do set "ESC=%%i"
set "green=%ESC%[92m"
set "cyan=%ESC%[96m"
set "red=%ESC%[91m"
set "reset=%ESC%[0m"
set "yellow=%ESC%[93m"

REM Parâmetros
set SERVER_NAME="VRising Server"
set SAVE_NAME="crepusculo"
set DATA_PATH=save-data
set LOG_PATH=logs
set BACKUP_PATH=backups

REM Cria pastas se não existirem
if not exist "%LOG_PATH%" mkdir "%LOG_PATH%"
if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"

REM Timestamp
for /f %%i in ('powershell -command "Get-Date -Format yyyy-MM-dd_HH-mm-ss"') do set TIMESTAMP=%%i
set LOG_FILE=%LOG_PATH%\server_%TIMESTAMP%.log
set BACKUP_FILE=%BACKUP_PATH%\backup_%SAVE_NAME%_%TIMESTAMP%.zip

echo %cyan%========================================================%reset%
echo %cyan%      V RISING SERVER - INICIANDO                       %reset%
echo %cyan%========================================================%reset%
echo %yellow%[1/4]%reset% Salvando backup automático...
powershell Compress-Archive -Path "%DATA_PATH%\Saves" -DestinationPath "%BACKUP_FILE%" >nul 2>&1

echo %yellow%[2/4]%reset% Iniciando servidor...
echo %green%Logs serão exibidos aqui e salvos em: %LOG_FILE%%reset%

REM Início com reinício automático se travar
:REINICIAR
echo %cyan%--- NOVA SESSÃO: %DATE% %TIME% --- %reset% >> "%LOG_FILE%"

REM Executa o servidor e duplica saída para o arquivo e para o console
powershell -Command "& { .\VRisingServer.exe -persistentDataPath '%DATA_PATH%' -serverName %SERVER_NAME% -saveName %SAVE_NAME% -port 9876 -diag-job-temp-memory-leak-validation 2>&1 | Tee-Object -FilePath '%LOG_FILE%' }"

echo %red%Servidor foi encerrado inesperadamente. Reiniciando...%reset%
timeout /t 5 >nul
goto REINICIAR
