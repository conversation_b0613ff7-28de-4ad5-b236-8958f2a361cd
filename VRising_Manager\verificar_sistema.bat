@echo off
chcp 65001 >nul
title Verificação do Sistema - V Rising Manager

echo 🔍 Verificação do Sistema V Rising Manager
echo ==========================================
echo.

REM Verificar Python
echo [1/5] Verificando Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python não encontrado
    set PYTHON_OK=0
) else (
    echo ✅ Python encontrado
    python --version
    set PYTHON_OK=1
)
echo.

REM Verificar pip
echo [2/5] Verificando pip...
if %PYTHON_OK%==1 (
    pip --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ pip não encontrado
        set PIP_OK=0
    ) else (
        echo ✅ pip encontrado
        set PIP_OK=1
    )
) else (
    echo ⏭️ Pulando (Python não disponível)
    set PIP_OK=0
)
echo.

REM Verificar dependências
echo [3/5] Verificando dependências...
if %PYTHON_OK%==1 (
    python -c "import customtkinter; print('✅ customtkinter OK')" 2>nul
    if errorlevel 1 (
        echo ❌ customtkinter não instalado
        set DEPS_OK=0
    ) else (
        set DEPS_OK=1
    )
    
    python -c "import psutil; print('✅ psutil OK')" 2>nul
    if errorlevel 1 (
        echo ❌ psutil não instalado
        set DEPS_OK=0
    )
) else (
    echo ⏭️ Pulando (Python não disponível)
    set DEPS_OK=0
)
echo.

REM Verificar VRisingServer.exe
echo [4/5] Verificando servidor V Rising...
if exist "VRisingServer.exe" (
    echo ✅ VRisingServer.exe encontrado
    set SERVER_OK=1
) else (
    echo ❌ VRisingServer.exe não encontrado
    set SERVER_OK=0
)
echo.

REM Verificar arquivos do gerenciador
echo [5/5] Verificando arquivos do gerenciador...
if exist "vrising_manager.py" (
    echo ✅ vrising_manager.py encontrado
    set MANAGER_OK=1
) else (
    echo ❌ vrising_manager.py não encontrado
    set MANAGER_OK=0
)

if exist "requirements.txt" (
    echo ✅ requirements.txt encontrado
) else (
    echo ❌ requirements.txt não encontrado
)
echo.

REM Resumo
echo ==========================================
echo 📋 RESUMO DA VERIFICAÇÃO
echo ==========================================

if %PYTHON_OK%==1 (
    echo ✅ Python: OK
) else (
    echo ❌ Python: FALTANDO
)

if %PIP_OK%==1 (
    echo ✅ pip: OK
) else (
    echo ❌ pip: FALTANDO
)

if %DEPS_OK%==1 (
    echo ✅ Dependências: OK
) else (
    echo ❌ Dependências: FALTANDO
)

if %SERVER_OK%==1 (
    echo ✅ Servidor V Rising: OK
) else (
    echo ❌ Servidor V Rising: FALTANDO
)

if %MANAGER_OK%==1 (
    echo ✅ Gerenciador: OK
) else (
    echo ❌ Gerenciador: FALTANDO
)

echo.

REM Verificar se tudo está OK
if %PYTHON_OK%==1 if %DEPS_OK%==1 if %SERVER_OK%==1 if %MANAGER_OK%==1 (
    echo 🎉 SISTEMA PRONTO!
    echo Você pode executar: run_manager.bat
) else (
    echo ⚠️ PROBLEMAS ENCONTRADOS!
    echo.
    if %PYTHON_OK%==0 (
        echo 🔧 Para instalar Python:
        echo    - Acesse: https://python.org/downloads/
        echo    - Marque "Add Python to PATH" durante instalação
    )
    if %DEPS_OK%==0 if %PYTHON_OK%==1 (
        echo 🔧 Para instalar dependências:
        echo    - Execute: install.bat
    )
    if %SERVER_OK%==0 (
        echo 🔧 Para o servidor V Rising:
        echo    - Coloque este gerenciador na pasta do servidor
        echo    - Certifique-se que VRisingServer.exe existe
    )
)

echo.
pause
