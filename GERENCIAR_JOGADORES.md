# 👥 Gerenciamento de Jogadores - V Rising Server Manager

## 🎯 Funcionalidades Implementadas

### ✅ **Visualização de Jogadores Online**
- **Detecção Automática**: Analisa logs do servidor em tempo real
- **Informações Mostradas**:
  - Nome do jogador
  - Steam ID (17 dígitos)
  - Horário de conexão
  - Status online/offline
- **Atualização**: A cada 30 segundos automaticamente

### ✅ **Sistema de Administradores**
- **Tornar Admin**: Adiciona jogador à lista de administradores
- **Remover Admin**: Remove privilégios administrativos
- **Lista Visual**: Mostra todos os admins configurados
- **Arquivo**: Gerencia `adminlist.txt` automaticamente

### ✅ **Sistema de Banimento**
- **Banir Jogador**: Adiciona à lista de banidos
- **Desbanir Jogador**: Remove da lista de banidos
- **Lista Visual**: Mostra todos os jogadores banidos
- **Arquivo**: Gerencia `banlist.txt` automaticamente

## 🔧 Como Usar

### 1. **Encontrar Steam ID de um Jogador**
```
Métodos para obter Steam ID:
• Site: steamid.io
• Perfil Steam → Ver URL → números após /profiles/
• Logs do servidor (quando conectam)
• Exemplo: 76561198000000000
```

### 2. **Tornar Jogador Administrador**
1. Vá para aba **"👥 Jogadores"**
2. Digite o **Steam ID** no campo
3. Clique em **"👑 Tornar Admin"**
4. Jogador terá privilégios de admin no próximo login

### 3. **Remover Administrador**
1. Digite o **Steam ID** do admin
2. Clique em **"👤 Remover Admin"**
3. Privilégios serão removidos

### 4. **Banir Jogador**
1. Digite o **Steam ID** do jogador
2. Clique em **"🚫 Banir Jogador"**
3. Jogador não poderá mais conectar

### 5. **Desbanir Jogador**
1. Digite o **Steam ID** do jogador banido
2. Clique em **"✅ Desbanir Jogador"**
3. Jogador poderá conectar novamente

## 📊 Monitoramento em Tempo Real

### **Detecção de Jogadores Online**
O sistema analisa os logs do servidor procurando por:
- Mensagens de conexão de jogadores
- Mensagens de desconexão
- Steam IDs nas mensagens de log
- Timestamps de atividade

### **Padrões de Log Reconhecidos**
```
Exemplos de logs que o sistema detecta:
• "Player connected: NomeJogador (76561198000000000)"
• "Player disconnected: NomeJogador"
• "User authenticated: 76561198000000000"
```

## 📁 Arquivos Gerenciados

### **adminlist.txt**
```
Localização: save-data/Settings/adminlist.txt
Formato: Um Steam ID por linha
Exemplo:
76561198000000000
76561198111111111
```

### **banlist.txt**
```
Localização: save-data/Settings/banlist.txt
Formato: Um Steam ID por linha
Exemplo:
76561198222222222
76561198333333333
```

## 🎮 Comandos In-Game para Admins

### **Chat Commands (no jogo)**
```
!admin          - Verificar se você é admin
!kick [nome]    - Expulsar jogador
!ban [nome]     - Banir jogador
!unban [id]     - Desbanir por Steam ID
!players        - Listar jogadores online
```

## 🔧 Solução de Problemas

### ❌ **"Jogadores não aparecem na lista"**
**Possíveis causas:**
- Servidor offline
- Logs não estão sendo gerados
- Formato de log diferente do esperado

**Soluções:**
1. Verifique se o servidor está rodando
2. Confirme se há arquivos de log em `logs/`
3. Clique em "🔄 Atualizar Lista"

### ❌ **"Erro ao tornar admin"**
**Possíveis causas:**
- Steam ID inválido
- Permissões de arquivo
- Pasta Settings não existe

**Soluções:**
1. Verifique se o Steam ID tem 17 dígitos
2. Execute como administrador
3. Verifique se `save-data/Settings/` existe

### ❌ **"Admin não funciona no jogo"**
**Possíveis causas:**
- Servidor não foi reiniciado
- Steam ID incorreto
- Arquivo corrompido

**Soluções:**
1. Reinicie o servidor após adicionar admin
2. Confirme o Steam ID correto
3. Verifique o arquivo `adminlist.txt`

## 💡 Dicas Importantes

### **🔄 Reinicialização**
- Sempre reinicie o servidor após mudanças em admins/bans
- Use o botão "🔄 Reiniciar" do gerenciador

### **💾 Backup**
- Faça backup dos arquivos antes de modificar
- Use o sistema de backup automático do gerenciador

### **🔍 Monitoramento**
- Lista de jogadores atualiza automaticamente
- Para atualização manual, use "🔄 Atualizar Lista"

### **📋 Steam IDs**
- Sempre use Steam ID de 17 dígitos
- Não use nomes de usuário (podem mudar)
- Teste com steamid.io se necessário

## 🌐 Acesso Remoto

Para gerenciar jogadores remotamente:
1. Configure port forwarding no roteador
2. Libere porta no Windows Firewall
3. Use VPN se necessário (Hamachi, etc.)
4. Acesse via IP externo + porta

---

**Desenvolvido para facilitar o gerenciamento de servidores V Rising** 🧛‍♂️
