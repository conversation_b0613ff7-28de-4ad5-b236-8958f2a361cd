@echo off
echo ==========================================
echo V Rising Manager - Instalacao
echo ==========================================
echo.

echo Verificando Python...
python --version
if errorlevel 1 (
    echo.
    echo ERRO: Python nao encontrado!
    echo.
    echo Para instalar Python:
    echo 1. Acesse: https://python.org/downloads/
    echo 2. Baixe Python 3.8 ou superior
    echo 3. Marque "Add Python to PATH" na instalacao
    echo 4. Reinicie o computador
    echo 5. Execute este script novamente
    echo.
    pause
    exit /b 1
)

echo Python OK!
echo.

echo Instalando customtkinter...
pip install customtkinter
if errorlevel 1 (
    echo Tentando instalacao alternativa...
    pip install --user customtkinter
)

echo.
echo Instalando psutil...
pip install psutil
if errorlevel 1 (
    echo Tentando instalacao alternativa...
    pip install --user psutil
)

echo.
echo ==========================================
echo Instalacao concluida!
echo ==========================================
echo.
echo Para usar o manager:
echo 1. Copie esta pasta para o diretorio do servidor V Rising
echo 2. Execute: python vrising_manager.py
echo.
echo Ou use o script: run_manager.bat
echo.
pause
