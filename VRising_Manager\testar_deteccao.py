#!/usr/bin/env python3
"""
Script para testar a detecção de jogadores nos logs
"""

import os
import re

def test_player_detection():
    print("🧪 TESTE DE DETECÇÃO DE JOGADORES")
    print("=" * 50)
    
    # Exemplo de linha do log real
    test_line = "[09:16:16] User '{<PERSON><PERSON><PERSON> *************:53283}' '76561198832506980', approvedUserIndex: 0, Character: '<PERSON>' connected as ID '0,1', Entity '323167,1'."
    
    print(f"📝 Linha de teste:")
    print(f"   {test_line}")
    print()
    
    # Padrões de detecção
    connection_patterns = [
        r"User.*'(\d{17})'.*Character:\s*'([^']+)'.*connected.*Entity\s*'(\d+,\d+)'",  # Padrão completo
        r"User.*'(\d{17})'.*connected",  # Padrão simplificado
        r"(\d{17}).*Character:\s*'([^']+)'.*connected",  # Alternativo
        r".*'(\d{17})'.*connected"  # Genérico
    ]
    
    print("🔍 Testando padrões:")
    for i, pattern in enumerate(connection_patterns, 1):
        match = re.search(pattern, test_line, re.IGNORECASE)
        if match:
            print(f"✅ Padrão {i}: FUNCIONOU")
            print(f"   Steam ID: {match.group(1)}")
            if len(match.groups()) > 1:
                print(f"   Grupos: {match.groups()}")
        else:
            print(f"❌ Padrão {i}: Não funcionou")
    
    print()
    
    # Testar extração de nome do personagem
    char_match = re.search(r"Character:\s*'([^']+)'", test_line)
    if char_match:
        print(f"✅ Nome do personagem: {char_match.group(1)}")
    else:
        print("❌ Não conseguiu extrair nome do personagem")
    
    # Testar extração de timestamp
    time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', test_line)
    if time_match:
        print(f"✅ Timestamp: {time_match.group(1)}")
    else:
        print("❌ Não conseguiu extrair timestamp")
    
    print()
    print("📁 Verificando arquivos reais:")
    
    # Verificar logs reais
    logs_path = "../logs"
    if os.path.exists(logs_path):
        log_files = [f for f in os.listdir(logs_path) if f.endswith('.log')]
        if log_files:
            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(logs_path, f)))
            log_path = os.path.join(logs_path, latest_log)
            
            print(f"📄 Analisando: {latest_log}")
            
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # Procurar por linhas com jogadores
            player_lines = []
            for line in lines[-100:]:  # Últimas 100 linhas
                if 'connected' in line and any(char.isdigit() for char in line):
                    if re.search(r'\d{17}', line):  # Tem Steam ID
                        player_lines.append(line.strip())
            
            if player_lines:
                print(f"✅ Encontradas {len(player_lines)} linha(s) de conexão:")
                for line in player_lines[-3:]:  # Últimas 3
                    print(f"   {line}")
                    
                    # Testar detecção na linha real
                    for pattern in connection_patterns:
                        match = re.search(pattern, line, re.IGNORECASE)
                        if match:
                            steam_id = match.group(1)
                            char_match = re.search(r"Character:\s*'([^']+)'", line)
                            character_name = char_match.group(1) if char_match else "Desconhecido"
                            time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                            connect_time = time_match.group(1) if time_match else "?"
                            
                            print(f"      🎮 {character_name} ({steam_id}) às {connect_time}")
                            break
            else:
                print("❌ Nenhuma linha de conexão encontrada")
        else:
            print("❌ Nenhum arquivo de log encontrado")
    else:
        print("❌ Pasta de logs não encontrada")
    
    print()
    print("📋 RESUMO:")
    print("✅ Padrão de detecção: Funcionando")
    print("✅ Extração de dados: Funcionando") 
    print("💡 Se não aparecer no gerenciador, verifique:")
    print("   • Se o servidor está rodando")
    print("   • Se há jogadores conectados recentemente")
    print("   • Use o botão 'Debug Logs' no gerenciador")

if __name__ == "__main__":
    test_player_detection()
    input("\nPressione Enter para sair...")
