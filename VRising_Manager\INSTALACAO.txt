🧛 V RISING SERVER MANAGER - INSTALAÇÃO
=======================================

📋 PASSO A PASSO:

1. COPIAR ARQUIVOS
   - Copie toda a pasta "VRising_Manager" para o diretório do seu servidor V Rising
   - A estrutura deve ficar assim:
   
   VRisingServer/
   ├── VRisingServer.exe          ← Seu servidor
   ├── VRising_Manager/           ← Esta pasta
   │   ├── vrising_manager.py
   │   ├── instalar.bat
   │   └── outros arquivos...
   ├── save-data/
   ├── logs/
   └── backups/

2. INSTALAR PYTHON (se não tiver)
   - Acesse: https://python.org/downloads/
   - Baixe Python 3.8 ou superior
   - IMPORTANTE: Marque "Add Python to PATH" durante a instalação
   - Reinicie o computador após instalar

3. INSTALAR DEPENDÊNCIAS
   - Entre na pasta VRising_Manager
   - Execute: instalar.bat
   - Aguarde a instalação das dependências

4. EXECUTAR O MANAGER
   - Execute: run_manager.bat
   - Ou: python vrising_manager.py

🔧 SOLUÇÃO DE PROBLEMAS:

❌ "Python não encontrado"
   → Instale Python e marque "Add to PATH"
   → Reinicie o computador

❌ "Dependências não instaladas"
   → Execute instalar.bat
   → Ou manualmente: pip install customtkinter psutil

❌ "VRisingServer.exe não encontrado"
   → Certifique-se que a pasta está no local correto
   → Veja a estrutura de pastas acima

❌ Interface não abre
   → Teste: python -m tkinter
   → Atualize Python para versão mais recente

📞 PRECISA DE AJUDA?
   - Leia: COMO_USAR.md (guia completo)
   - Leia: README_MANAGER.md (documentação técnica)
   - Execute: verificar_sistema.bat (diagnóstico)

🎮 BOA SORTE COM SEU SERVIDOR V RISING!
