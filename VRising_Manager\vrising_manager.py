#!/usr/bin/env python3
"""
V Rising Server Manager
Interface moderna para gerenciar servidor V Rising
"""

import tkinter as tk
import sys
import os

# Verificar dependências antes de importar
try:
    import customtkinter as ctk
    import psutil
except ImportError as e:
    print(f"❌ Erro: Dependência não encontrada: {e}")
    print("\n📦 Para instalar as dependências:")
    print("pip install customtkinter psutil")
    print("\nOu execute install.bat")
    input("\nPressione Enter para sair...")
    sys.exit(1)

import subprocess
import threading
import json
import datetime
import zipfile
from pathlib import Path
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Configuração do tema
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class LogFileHandler(FileSystemEventHandler):
    """Handler para monitorar mudanças nos arquivos de log em tempo real"""

    def __init__(self, manager):
        super().__init__()
        self.manager = manager
        self.last_positions = {}  # Arquivo -> última posição lida

    def on_modified(self, event):
        """Chamado quando um arquivo de log é modificado"""
        if event.is_directory:
            return

        # Verificar se é um arquivo de log
        if not event.src_path.endswith('.log'):
            return

        print(f"Debug: 📝 Arquivo de log modificado: {event.src_path}")

        try:
            self.process_log_file(event.src_path)
        except Exception as e:
            print(f"Erro ao processar log {event.src_path}: {e}")

    def process_log_file(self, log_path):
        """Processa novas linhas de um arquivo de log"""
        try:
            # Obter posição atual do arquivo
            current_size = os.path.getsize(log_path)
            last_position = self.last_positions.get(log_path, 0)

            if current_size <= last_position:
                return  # Arquivo não cresceu

            # Ler apenas as novas linhas
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(last_position)
                new_content = f.read()
                self.last_positions[log_path] = f.tell()

            if not new_content.strip():
                return

            # Processar novas linhas
            new_lines = new_content.split('\n')
            print(f"Debug: 📄 Processando {len(new_lines)} novas linhas")
            self.analyze_log_lines(new_lines, log_path)

        except Exception as e:
            print(f"Erro ao ler arquivo {log_path}: {e}")

    def analyze_log_lines(self, lines, log_path):
        """Analisa linhas do log em busca de eventos de jogadores"""
        import re

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Debug: mostrar linhas que podem ser relevantes
            if any(keyword in line.lower() for keyword in ['user', 'player', 'connect', 'character', 'steam']):
                print(f"Debug: 🔍 Linha relevante: {line[:100]}...")

            # Padrões de conexão baseados na pesquisa
            connection_patterns = [
                # Padrão específico do V Rising encontrado na pesquisa
                r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected",
                # Padrões alternativos
                r"'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                r"(\d{17}).*Character:\s*'([^']+)'.*connected",
                r"User.*'(\d{17})'.*connected",
                r"(\d{17}).*connected"
            ]

            # Verificar conexões
            for i, pattern in enumerate(connection_patterns):
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    steam_id = match.group(1)

                    # Extrair nome do personagem
                    character_name = f"Jogador_{steam_id[-4:]}"
                    if len(match.groups()) > 1 and match.group(2):
                        character_name = match.group(2)

                    # Extrair timestamp
                    time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                    connect_time = time_match.group(1) if time_match else "Agora"

                    # Notificar o manager
                    self.manager.on_player_connected(steam_id, character_name, connect_time, log_path)
                    break

            # Verificar desconexões
            disconnect_patterns = [
                r"User.*'(\d{17})'.*disconnected",
                r"'(\d{17})'.*disconnected",
                r"(\d{17}).*disconnected"
            ]

            for pattern in disconnect_patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    steam_id = match.group(1)
                    self.manager.on_player_disconnected(steam_id, log_path)
                    break

class VRisingManager:
    def __init__(self):
        # Verificar se o executável do servidor existe (na pasta pai)
        self.server_exe = "../VRisingServer.exe"
        if not os.path.exists(self.server_exe):
            # Tentar na pasta atual também
            self.server_exe = "VRisingServer.exe"
            if not os.path.exists(self.server_exe):
                self.show_error_and_exit(
                    "❌ VRisingServer.exe não encontrado!\n\n"
                    "Certifique-se de que a pasta VRising_Manager está\n"
                    "no diretório do servidor V Rising dedicado.\n\n"
                    "Estrutura esperada:\n"
                    "VRisingServer/\n"
                    "├── VRisingServer.exe\n"
                    "└── VRising_Manager/\n"
                    "    └── vrising_manager.py"
                )

        self.root = ctk.CTk()
        self.root.title("V Rising Server Manager")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Variáveis de controle
        self.server_process = None
        self.server_running = False
        self.monitoring_active = False

        # Sistema de monitoramento de jogadores
        self.last_log_position = 0
        self.known_players = {}  # Steam ID -> player info
        self.log_observer = None  # Watchdog observer para monitoramento em tempo real

        # Inicializar monitoramento de logs em tempo real
        self.start_log_monitoring()

        # Caminhos (relativos à pasta do servidor)
        # Verificar se estamos na pasta do manager ou na pasta do servidor
        if os.path.exists("../save-data"):
            # Executando da pasta VRising_Manager
            self.data_path = "../save-data"
            self.logs_path = "../logs"
            self.backups_path = "../backups"
        else:
            # Executando da pasta do servidor
            self.data_path = "save-data"
            self.logs_path = "logs"
            self.backups_path = "backups"

        # Configurações padrão
        self.config = {
            "server_name": "VRising Server",
            "save_name": "crepusculo",
            "port": 9876,
            "max_players": 40,
            "auto_backup": True,
            "backup_interval": 30  # minutos
        }

        self.load_config()
        self.setup_ui()
        self.start_monitoring()

        # Configurar fechamento da janela
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def show_error_and_exit(self, message):
        """Mostra erro crítico e sai da aplicação"""
        import tkinter.messagebox as msgbox
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        msgbox.showerror("Erro Crítico", message)
        root.destroy()
        sys.exit(1)

    def setup_ui(self):
        """Configura a interface principal"""
        # Frame principal
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Título
        title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧛 V Rising Server Manager",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(10, 20))

        # Status do servidor
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", padx=20, pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="● Servidor Offline",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="red"
        )
        self.status_label.pack(side="left", padx=20, pady=10)

        # Botões de controle
        self.control_frame = ctk.CTkFrame(self.status_frame)
        self.control_frame.pack(side="right", padx=20, pady=10)

        self.start_btn = ctk.CTkButton(
            self.control_frame,
            text="▶️ Iniciar",
            command=self.start_server,
            width=100
        )
        self.start_btn.pack(side="left", padx=5)

        self.stop_btn = ctk.CTkButton(
            self.control_frame,
            text="⏹️ Parar",
            command=self.stop_server,
            width=100,
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=5)

        self.restart_btn = ctk.CTkButton(
            self.control_frame,
            text="🔄 Reiniciar",
            command=self.restart_server,
            width=100,
            state="disabled"
        )
        self.restart_btn.pack(side="left", padx=5)

        # Notebook para abas
        self.notebook = ctk.CTkTabview(self.main_frame)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Criar abas
        self.create_dashboard_tab()
        self.create_config_tab()
        self.create_backup_tab()
        self.create_players_tab()
        self.create_logs_tab()
        self.create_mods_tab()

    def create_dashboard_tab(self):
        """Cria a aba do dashboard com gráficos"""
        tab = self.notebook.add("📊 Dashboard")

        # Frame principal com scroll
        main_frame = ctk.CTkScrollableFrame(tab)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Frame de estatísticas em tempo real
        stats_frame = ctk.CTkFrame(main_frame)
        stats_frame.pack(fill="x", pady=(0, 10))

        stats_title = ctk.CTkLabel(stats_frame, text="📊 Estatísticas em Tempo Real", font=ctk.CTkFont(size=18, weight="bold"))
        stats_title.pack(pady=(10, 5))

        # Grid de estatísticas
        self.stats_grid = ctk.CTkFrame(stats_frame)
        self.stats_grid.pack(fill="x", padx=10, pady=10)

        # Labels de estatísticas
        self.uptime_label = ctk.CTkLabel(self.stats_grid, text="⏱️ Uptime: --", font=ctk.CTkFont(size=14))
        self.uptime_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        self.players_label = ctk.CTkLabel(self.stats_grid, text="👥 Jogadores: 0/40", font=ctk.CTkFont(size=14))
        self.players_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")

        self.cpu_label = ctk.CTkLabel(self.stats_grid, text="💻 CPU: --%", font=ctk.CTkFont(size=14))
        self.cpu_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        self.memory_label = ctk.CTkLabel(self.stats_grid, text="🧠 RAM: -- MB", font=ctk.CTkFont(size=14))
        self.memory_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")

        # Frame para gráficos
        charts_frame = ctk.CTkFrame(main_frame)
        charts_frame.pack(fill="x", pady=(0, 10))

        charts_title = ctk.CTkLabel(charts_frame, text="📈 Gráficos de Performance", font=ctk.CTkFont(size=16, weight="bold"))
        charts_title.pack(pady=(10, 5))

        # Botão para gerar gráficos
        generate_btn = ctk.CTkButton(charts_frame, text="📊 Gerar Gráficos", command=self.generate_charts)
        generate_btn.pack(pady=5)

        # Container para os gráficos
        self.charts_container = ctk.CTkFrame(charts_frame)
        self.charts_container.pack(fill="x", padx=10, pady=10)

        # Inicializar dados para gráficos
        self.server_stats = {
            'timestamps': [],
            'player_count': [],
            'cpu_usage': [],
            'memory_usage': [],
            'uptime_minutes': []
        }

        # Console de logs em tempo real
        console_frame = ctk.CTkFrame(main_frame)
        console_frame.pack(fill="both", expand=True)

        console_title = ctk.CTkLabel(console_frame, text="📋 Console do Servidor", font=ctk.CTkFont(size=16, weight="bold"))
        console_title.pack(pady=(10, 5))

        self.console_text = ctk.CTkTextbox(console_frame, height=250, wrap="word")
        self.console_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_config_tab(self):
        """Cria a aba de configurações"""
        tab = self.notebook.add("⚙️ Configurações")

        # Frame scrollável
        scroll_frame = ctk.CTkScrollableFrame(tab)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Configurações do servidor
        server_frame = ctk.CTkFrame(scroll_frame)
        server_frame.pack(fill="x", pady=10)

        ctk.CTkLabel(server_frame, text="Configurações do Servidor", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        # Nome do servidor
        ctk.CTkLabel(server_frame, text="Nome do Servidor:").pack(anchor="w", padx=10)
        self.server_name_entry = ctk.CTkEntry(server_frame, width=300)
        self.server_name_entry.pack(padx=10, pady=(0, 10))
        self.server_name_entry.insert(0, self.config["server_name"])

        # Nome do save
        ctk.CTkLabel(server_frame, text="Nome do Save:").pack(anchor="w", padx=10)
        self.save_name_entry = ctk.CTkEntry(server_frame, width=300)
        self.save_name_entry.pack(padx=10, pady=(0, 10))
        self.save_name_entry.insert(0, self.config["save_name"])

        # Porta
        ctk.CTkLabel(server_frame, text="Porta:").pack(anchor="w", padx=10)
        self.port_entry = ctk.CTkEntry(server_frame, width=300)
        self.port_entry.pack(padx=10, pady=(0, 10))
        self.port_entry.insert(0, str(self.config["port"]))

        # Máximo de jogadores
        ctk.CTkLabel(server_frame, text="Máximo de Jogadores:").pack(anchor="w", padx=10)
        self.max_players_entry = ctk.CTkEntry(server_frame, width=300)
        self.max_players_entry.pack(padx=10, pady=(0, 10))
        self.max_players_entry.insert(0, str(self.config["max_players"]))

        # Botão salvar
        save_btn = ctk.CTkButton(server_frame, text="💾 Salvar Configurações", command=self.save_config)
        save_btn.pack(pady=10)

    def create_backup_tab(self):
        """Cria a aba de backups"""
        tab = self.notebook.add("💾 Backups")

        # Controles de backup
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(control_frame, text="Gerenciamento de Backups", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        btn_frame = ctk.CTkFrame(control_frame)
        btn_frame.pack(pady=10)

        backup_btn = ctk.CTkButton(btn_frame, text="📦 Criar Backup", command=self.create_backup)
        backup_btn.pack(side="left", padx=5)

        restore_btn = ctk.CTkButton(btn_frame, text="📥 Restaurar Backup", command=self.restore_backup)
        restore_btn.pack(side="left", padx=5)

        # Lista de backups
        list_frame = ctk.CTkFrame(tab)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)

        ctk.CTkLabel(list_frame, text="Backups Disponíveis", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        self.backup_listbox = ctk.CTkTextbox(list_frame, height=300, wrap="word")
        self.backup_listbox.pack(fill="both", expand=True, padx=10, pady=10)

        self.refresh_backup_list()

    def create_players_tab(self):
        """Cria a aba de jogadores"""
        tab = self.notebook.add("👥 Jogadores")

        # Frame de controles
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(control_frame, text="Gerenciamento de Jogadores", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        # Botões de atualização
        btn_frame = ctk.CTkFrame(control_frame)
        btn_frame.pack(pady=10)

        refresh_players_btn = ctk.CTkButton(btn_frame, text="🔄 Atualizar Lista", command=self.refresh_players)
        refresh_players_btn.pack(side="left", padx=5)

        debug_btn = ctk.CTkButton(btn_frame, text="🔍 Debug Detecção", command=self.debug_player_detection)
        debug_btn.pack(side="left", padx=5)

        force_btn = ctk.CTkButton(btn_frame, text="⚡ Forçar Scan", command=self.force_scan_logs)
        force_btn.pack(side="left", padx=5)

        test_monitor_btn = ctk.CTkButton(btn_frame, text="🔧 Testar Monitor", command=self.test_monitoring)
        test_monitor_btn.pack(side="left", padx=5)

        # Frame principal dividido em duas colunas
        main_frame = ctk.CTkFrame(tab)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna esquerda - Jogadores Online
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)

        ctk.CTkLabel(left_frame, text="👥 Jogadores Online", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        self.players_listbox = ctk.CTkTextbox(left_frame, height=200, wrap="word")
        self.players_listbox.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna direita - Ações
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", padx=(5, 10), pady=10, expand=False)
        right_frame.configure(width=250)  # Largura fixa maior

        ctk.CTkLabel(right_frame, text="⚙️ Ações", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        # Campo para inserir Steam ID ou nome
        self.player_id_entry = ctk.CTkEntry(right_frame, width=200, placeholder_text="Steam ID: Ex: 76561198000000000")
        self.player_id_entry.pack(padx=10, pady=(10, 5))

        # Botões de ação - 4 botões em uma linha
        buttons_frame = ctk.CTkFrame(right_frame)
        buttons_frame.pack(padx=10, pady=5, fill="x")

        admin_btn = ctk.CTkButton(buttons_frame, text="👑", command=self.make_admin, width=50, height=35)
        admin_btn.grid(row=0, column=0, padx=1, pady=2)

        remove_admin_btn = ctk.CTkButton(buttons_frame, text="👤", command=self.remove_admin, width=50, height=35)
        remove_admin_btn.grid(row=0, column=1, padx=1, pady=2)

        ban_btn = ctk.CTkButton(buttons_frame, text="🚫", command=self.ban_player, width=50, height=35, fg_color="red")
        ban_btn.grid(row=0, column=2, padx=1, pady=2)

        unban_btn = ctk.CTkButton(buttons_frame, text="✅", command=self.unban_player, width=50, height=35, fg_color="green")
        unban_btn.grid(row=0, column=3, padx=1, pady=2)

        # Configurar grid para distribuir uniformemente
        for i in range(4):
            buttons_frame.grid_columnconfigure(i, weight=1)

        # Separador
        separator = ctk.CTkFrame(right_frame, height=1)
        separator.pack(fill="x", padx=10, pady=5)

        # Notebook para listas de admins e banidos
        lists_notebook = ctk.CTkTabview(right_frame, height=350)
        lists_notebook.pack(padx=10, pady=5, fill="both", expand=True)

        # Aba de Administradores
        admin_tab = lists_notebook.add("👑 Admins")

        # Frame scrollável para admins
        admin_scroll_frame = ctk.CTkScrollableFrame(admin_tab)
        admin_scroll_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.admins_listbox = ctk.CTkTextbox(admin_scroll_frame, height=300, wrap="word")
        self.admins_listbox.pack(fill="both", expand=True)

        # Aba de Banidos
        banned_tab = lists_notebook.add("🚫 Banidos")

        # Frame scrollável para banidos
        banned_scroll_frame = ctk.CTkScrollableFrame(banned_tab)
        banned_scroll_frame.pack(fill="both", expand=True, padx=5, pady=5)

        self.banned_listbox = ctk.CTkTextbox(banned_scroll_frame, height=300, wrap="word")
        self.banned_listbox.pack(fill="both", expand=True)



        # Inicializar listas
        self.refresh_players()
        self.refresh_admin_lists()

        # Configurar callback para quando a aba for selecionada
        def on_tab_change():
            if self.notebook.get() == "👥 Jogadores":
                self.root.after(100, self.refresh_admin_lists)  # Atualizar após 100ms

        self.notebook.configure(command=on_tab_change)

    def create_logs_tab(self):
        """Cria a aba de logs"""
        tab = self.notebook.add("📋 Logs")

        # Controles
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        refresh_btn = ctk.CTkButton(control_frame, text="🔄 Atualizar", command=self.refresh_logs)
        refresh_btn.pack(side="left", padx=10, pady=10)

        clear_btn = ctk.CTkButton(control_frame, text="🗑️ Limpar", command=self.clear_logs)
        clear_btn.pack(side="left", padx=10, pady=10)

        # Área de logs
        self.logs_text = ctk.CTkTextbox(tab, height=400, wrap="word")
        self.logs_text.pack(fill="both", expand=True, padx=10, pady=10)

    def load_config(self):
        """Carrega configurações do arquivo"""
        config_file = "vrising_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config.update(json.load(f))
            except Exception as e:
                print(f"Erro ao carregar configurações: {e}")

    def save_config(self):
        """Salva configurações no arquivo"""
        try:
            self.config["server_name"] = self.server_name_entry.get()
            self.config["save_name"] = self.save_name_entry.get()
            self.config["port"] = int(self.port_entry.get())
            self.config["max_players"] = int(self.max_players_entry.get())

            with open("vrising_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            # Mostrar confirmação
            self.show_message("✅ Configurações salvas com sucesso!")

        except Exception as e:
            self.show_message(f"❌ Erro ao salvar configurações: {e}")

    def show_message(self, message):
        """Mostra mensagem no console"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.console_text.insert("end", f"[{timestamp}] {message}\n")
        self.console_text.see("end")

    def start_server(self):
        """Inicia o servidor V Rising"""
        if self.server_running:
            self.show_message("⚠️ Servidor já está rodando!")
            return

        try:
            # Criar diretórios necessários
            os.makedirs(self.logs_path, exist_ok=True)
            os.makedirs(self.backups_path, exist_ok=True)

            # Criar backup automático antes de iniciar
            if self.config.get("auto_backup", True):
                self.create_backup(auto=True)

            # Comando para iniciar o servidor
            # Usar caminho absoluto para data path
            data_path_abs = os.path.abspath(self.data_path)

            cmd = [
                self.server_exe,
                "-persistentDataPath", data_path_abs,
                "-serverName", self.config["server_name"],
                "-saveName", self.config["save_name"],
                "-port", str(self.config["port"])
            ]

            self.show_message(f"🚀 Iniciando servidor: {' '.join(cmd)}")

            # Iniciar processo em thread separada
            def run_server():
                try:
                    self.server_process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        universal_newlines=True,
                        bufsize=1
                    )

                    self.server_running = True
                    self.root.after(0, self.update_server_status)

                    # Ler output do servidor
                    for line in iter(self.server_process.stdout.readline, ''):
                        if line:
                            self.root.after(0, lambda l=line: self.show_message(l.strip()))

                    self.server_process.wait()

                except Exception as e:
                    self.root.after(0, lambda: self.show_message(f"❌ Erro ao iniciar servidor: {e}"))
                finally:
                    self.server_running = False
                    self.server_process = None
                    self.root.after(0, self.update_server_status)

            threading.Thread(target=run_server, daemon=True).start()

        except Exception as e:
            self.show_message(f"❌ Erro ao iniciar servidor: {e}")

    def stop_server(self):
        """Para o servidor V Rising"""
        if not self.server_running or not self.server_process:
            self.show_message("⚠️ Servidor não está rodando!")
            return

        try:
            self.show_message("🛑 Parando servidor...")
            self.server_process.terminate()

            # Aguardar um pouco e forçar se necessário
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.show_message("⚠️ Forçando parada do servidor...")
                self.server_process.kill()

            self.server_running = False
            self.server_process = None
            self.update_server_status()
            self.show_message("✅ Servidor parado com sucesso!")

        except Exception as e:
            self.show_message(f"❌ Erro ao parar servidor: {e}")

    def restart_server(self):
        """Reinicia o servidor V Rising"""
        self.show_message("🔄 Reiniciando servidor...")
        self.stop_server()

        # Aguardar um pouco antes de reiniciar
        def delayed_start():
            time.sleep(3)
            self.root.after(0, self.start_server)

        threading.Thread(target=delayed_start, daemon=True).start()

    def update_server_status(self):
        """Atualiza o status visual do servidor"""
        if self.server_running:
            self.status_label.configure(text="● Servidor Online", text_color="green")
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.restart_btn.configure(state="normal")
        else:
            self.status_label.configure(text="● Servidor Offline", text_color="red")
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.restart_btn.configure(state="disabled")

    def create_backup(self, auto=False):
        """Cria backup dos dados do servidor"""
        try:
            if not os.path.exists(self.data_path):
                self.show_message("⚠️ Diretório de dados não encontrado!")
                return

            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"backup_{self.config['save_name']}_{timestamp}.zip"
            backup_path = os.path.join(self.backups_path, backup_name)

            prefix = "🤖 [AUTO]" if auto else "📦"
            self.show_message(f"{prefix} Criando backup: {backup_name}")

            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                saves_path = os.path.join(self.data_path, "Saves")
                if os.path.exists(saves_path):
                    for root, _, files in os.walk(saves_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, self.data_path)
                            zipf.write(file_path, arcname)

            self.show_message(f"✅ Backup criado: {backup_name}")
            self.refresh_backup_list()

        except Exception as e:
            self.show_message(f"❌ Erro ao criar backup: {e}")

    def restore_backup(self):
        """Restaura backup selecionado"""
        # Implementação simplificada - em produção seria mais robusta
        self.show_message("🚧 Funcionalidade de restauração em desenvolvimento")

    def refresh_backup_list(self):
        """Atualiza lista de backups"""
        try:
            self.backup_listbox.delete("1.0", "end")

            if not os.path.exists(self.backups_path):
                self.backup_listbox.insert("end", "Nenhum backup encontrado.\n")
                return

            backups = []
            for file in os.listdir(self.backups_path):
                if file.endswith('.zip'):
                    file_path = os.path.join(self.backups_path, file)
                    size = os.path.getsize(file_path)
                    size_mb = size / (1024 * 1024)
                    mtime = os.path.getmtime(file_path)
                    date_str = datetime.datetime.fromtimestamp(mtime).strftime("%d/%m/%Y %H:%M")
                    backups.append((file, date_str, size_mb))

            backups.sort(key=lambda x: x[1], reverse=True)

            for backup, date, size in backups:
                self.backup_listbox.insert("end", f"📦 {backup}\n")
                self.backup_listbox.insert("end", f"   📅 {date} | 💾 {size:.1f} MB\n\n")

        except Exception as e:
            self.backup_listbox.insert("end", f"❌ Erro ao listar backups: {e}\n")

    def refresh_logs(self):
        """Atualiza visualização de logs"""
        try:
            self.logs_text.delete("1.0", "end")

            if not os.path.exists(self.logs_path):
                self.logs_text.insert("end", "Nenhum log encontrado.\n")
                return

            # Pegar o log mais recente
            log_files = [f for f in os.listdir(self.logs_path) if f.endswith('.log')]
            if not log_files:
                self.logs_text.insert("end", "Nenhum arquivo de log encontrado.\n")
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(self.logs_path, f)))
            log_path = os.path.join(self.logs_path, latest_log)

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                self.logs_text.insert("end", content)

            self.logs_text.see("end")

        except Exception as e:
            self.logs_text.insert("end", f"❌ Erro ao carregar logs: {e}\n")

    def clear_logs(self):
        """Limpa visualização de logs"""
        self.logs_text.delete("1.0", "end")

    def create_mods_tab(self):
        """Cria a aba de mods"""
        tab = self.notebook.add("🔧 Mods")

        # Frame principal dividido em duas colunas
        main_frame = ctk.CTkFrame(tab)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna esquerda - Lista de mods
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)

        ctk.CTkLabel(left_frame, text="📦 Mods Instalados", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        # Controles de mods
        controls_frame = ctk.CTkFrame(left_frame)
        controls_frame.pack(fill="x", padx=10, pady=5)

        refresh_mods_btn = ctk.CTkButton(controls_frame, text="🔄 Atualizar", command=self.refresh_mods)
        refresh_mods_btn.pack(side="left", padx=5, pady=5)

        scan_mods_btn = ctk.CTkButton(controls_frame, text="🔍 Escanear", command=self.scan_mods)
        scan_mods_btn.pack(side="left", padx=5, pady=5)

        # Lista de mods
        self.mods_listbox = ctk.CTkTextbox(left_frame, height=400, wrap="word")
        self.mods_listbox.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna direita - Informações e ações (com scroll)
        right_frame = ctk.CTkScrollableFrame(main_frame)
        right_frame.pack(side="right", fill="both", padx=(5, 10), pady=10)
        right_frame.configure(width=300)

        ctk.CTkLabel(right_frame, text="ℹ️ Informações", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        # Informações sobre mods
        info_frame = ctk.CTkFrame(right_frame)
        info_frame.pack(fill="both", expand=True, padx=10, pady=5)

        self.mod_info_text = ctk.CTkTextbox(info_frame, height=250, wrap="word")
        self.mod_info_text.pack(fill="both", expand=True, padx=5, pady=5)

        # Ações com mods
        actions_frame = ctk.CTkFrame(right_frame)
        actions_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(actions_frame, text="⚙️ Ações", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(5, 10))

        # Campo para URL/caminho do mod
        ctk.CTkLabel(actions_frame, text="URL ou Caminho do Mod:").pack(anchor="w", padx=5)
        self.mod_url_entry = ctk.CTkEntry(actions_frame, width=250, placeholder_text="https://thunderstore.io/...")
        self.mod_url_entry.pack(padx=5, pady=5)

        # Botões de ação
        install_btn = ctk.CTkButton(actions_frame, text="📥 Instalar Mod", command=self.install_mod, width=200)
        install_btn.pack(padx=5, pady=2)

        remove_btn = ctk.CTkButton(actions_frame, text="🗑️ Remover Mod", command=self.remove_mod, width=200, fg_color="red")
        remove_btn.pack(padx=5, pady=2)

        enable_btn = ctk.CTkButton(actions_frame, text="✅ Ativar Mod", command=self.enable_mod, width=200, fg_color="green")
        enable_btn.pack(padx=5, pady=2)

        disable_btn = ctk.CTkButton(actions_frame, text="❌ Desativar Mod", command=self.disable_mod, width=200, fg_color="orange")
        disable_btn.pack(padx=5, pady=2)

        # Informações úteis
        help_frame = ctk.CTkFrame(right_frame)
        help_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(help_frame, text="💡 Dicas", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(5, 5))

        help_text = ctk.CTkTextbox(help_frame, height=120, wrap="word")
        help_text.pack(fill="both", expand=True, padx=5, pady=5)

        # Adicionar texto de ajuda
        help_content = """🔧 GERENCIAMENTO DE MODS

📁 Pasta de Mods:
BepInEx/plugins/

🌐 Sites de Mods:
• Thunderstore.io
• NexusMods
• GitHub

⚠️ IMPORTANTE:
• Sempre faça backup antes
• Teste mods um por vez
• Verifique compatibilidade
• Reinicie servidor após mudanças

📋 Formatos Suportados:
• .dll (plugins)
• .zip (pacotes)
• Pastas de mods"""

        help_text.insert("1.0", help_content)
        help_text.configure(state="disabled")

        # Inicializar lista de mods
        self.refresh_mods()

    def start_monitoring(self):
        """Inicia monitoramento do sistema"""
        self.monitoring_active = True

        def monitor():
            player_update_counter = 0
            chart_update_counter = 0
            while self.monitoring_active:
                try:
                    if self.server_running and self.server_process:
                        # Atualizar estatísticas
                        process = psutil.Process(self.server_process.pid)
                        cpu_percent = process.cpu_percent()
                        memory_mb = process.memory_info().rss / (1024 * 1024)

                        # Calcular uptime
                        create_time = process.create_time()
                        uptime_seconds = time.time() - create_time
                        uptime_str = str(datetime.timedelta(seconds=int(uptime_seconds)))

                        # Atualizar UI
                        self.root.after(0, lambda: self.update_stats(uptime_str, cpu_percent, memory_mb))

                        # Atualizar lista de jogadores a cada 15 segundos
                        player_update_counter += 1
                        if player_update_counter >= 3:  # 3 * 5 segundos = 15 segundos
                            self.root.after(0, self.refresh_players)
                            # Backup: scan manual se watchdog não estiver funcionando
                            self.root.after(0, self.backup_player_scan)
                            player_update_counter = 0

                        # Atualizar gráficos a cada 30 segundos
                        chart_update_counter += 1
                        if chart_update_counter >= 6:  # 6 * 5 segundos = 30 segundos
                            try:
                                self.root.after(0, self.generate_charts)
                            except:
                                pass  # Ignorar erros de gráficos
                            chart_update_counter = 0

                except (psutil.NoSuchProcess, psutil.AccessDenied, AttributeError):
                    pass
                except Exception as e:
                    print(f"Erro no monitoramento: {e}")

                time.sleep(5)  # Atualizar a cada 5 segundos

        threading.Thread(target=monitor, daemon=True).start()

    def update_stats(self, uptime, cpu, memory):
        """Atualiza estatísticas na UI"""
        try:
            self.uptime_label.configure(text=f"⏱️ Uptime: {uptime}")
            self.cpu_label.configure(text=f"💻 CPU: {cpu:.1f}%")
            self.memory_label.configure(text=f"🧠 RAM: {memory:.1f} MB")
        except:
            pass

    def refresh_players(self):
        """Atualiza lista de jogadores online"""
        try:
            self.players_listbox.delete("1.0", "end")

            if not self.server_running:
                self.players_listbox.insert("end", "⚠️ Servidor offline\n")
                self.players_listbox.insert("end", "Inicie o servidor para monitorar jogadores\n")
                return

            # Verificar novos eventos nos logs
            self.check_player_events()

            # Mostrar jogadores online
            online_players = [p for p in self.known_players.values() if p['status'] == 'online']

            if online_players:
                self.players_listbox.insert("end", f"👥 Jogadores Online ({len(online_players)}):\n")
                self.players_listbox.insert("end", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")

                for i, player in enumerate(online_players, 1):
                    self.players_listbox.insert("end", f"{i}. 🎮 {player['name']}\n")
                    self.players_listbox.insert("end", f"   Steam ID: {player['steam_id']}\n")
                    self.players_listbox.insert("end", f"   Conectado: {player['connect_time']}\n\n")
            else:
                self.players_listbox.insert("end", "📊 Nenhum jogador online\n\n")
                self.players_listbox.insert("end", "💡 O sistema detectará automaticamente quando\n")
                self.players_listbox.insert("end", "   jogadores se conectarem ao servidor.\n")

        except Exception as e:
            self.players_listbox.insert("end", f"❌ Erro ao atualizar jogadores: {e}\n")

    def check_player_events(self):
        """Verifica novos eventos de conexão/desconexão nos logs em tempo real"""
        try:
            # Tentar múltiplos caminhos para logs
            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                if os.path.exists(path):
                    log_path_found = path
                    break

            if not log_path_found:
                return

            log_files = [f for f in os.listdir(log_path_found) if f.endswith('.log')]
            if not log_files:
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_path_found, f)))
            log_file_path = os.path.join(log_path_found, latest_log)

            # Verificar se o arquivo mudou
            current_size = os.path.getsize(log_file_path)
            if not hasattr(self, 'last_log_size'):
                self.last_log_size = 0

            if current_size <= self.last_log_size:
                return  # Arquivo não cresceu, nada novo

            # Ler apenas as novas linhas
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_log_size)
                new_content = f.read()
                self.last_log_size = f.tell()

            if not new_content.strip():
                return  # Nenhum conteúdo novo

            # Processar novas linhas
            import re
            new_lines = new_content.split('\n')

            print(f"Debug: Processando {len(new_lines)} novas linhas do log")

            for line in new_lines:
                line = line.strip()
                if not line:
                    continue

                # Debug: mostrar linhas que podem ser relevantes
                if any(keyword in line.lower() for keyword in ['user', 'player', 'connect', 'character', 'steam']):
                    print(f"Debug: Linha relevante: {line[:150]}...")

                # Padrões mais amplos para detectar conexões
                connection_patterns = [
                    # Padrão específico do V Rising
                    r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected",
                    # Padrões alternativos
                    r"'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                    r"(\d{17}).*Character:\s*'([^']+)'.*connected",
                    r"User.*'(\d{17})'.*connected",
                    r"Player.*'(\d{17})'.*connected",
                    # Padrão mais genérico
                    r"(\d{17}).*connected"
                ]

                for i, pattern in enumerate(connection_patterns):
                    connect_match = re.search(pattern, line, re.IGNORECASE)
                    if connect_match:
                        steam_id = connect_match.group(1)

                        # Tentar extrair nome do personagem
                        character_name = f"Jogador_{steam_id[-4:]}"
                        if len(connect_match.groups()) > 1 and connect_match.group(2):
                            character_name = connect_match.group(2)
                        else:
                            # Tentar encontrar nome em outros padrões na mesma linha
                            name_patterns = [
                                r"Character:\s*'([^']+)'",
                                r"Name:\s*'([^']+)'",
                                r"Player:\s*'([^']+)'"
                            ]
                            for name_pattern in name_patterns:
                                name_match = re.search(name_pattern, line, re.IGNORECASE)
                                if name_match:
                                    character_name = name_match.group(1)
                                    break

                        # Extrair timestamp
                        time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                        connect_time = time_match.group(1) if time_match else "Agora"

                        # Adicionar/atualizar jogador
                        self.known_players[steam_id] = {
                            'name': character_name,
                            'steam_id': steam_id,
                            'connect_time': connect_time,
                            'status': 'online'
                        }

                        print(f"Debug: ✅ JOGADOR CONECTADO: {character_name} ({steam_id}) - Padrão {i+1}")
                        self.show_message(f"🟢 {character_name} conectou ao servidor")
                        break

                # Verificar desconexões
                disconnect_patterns = [
                    r"User.*'(\d{17})'.*disconnected",
                    r"'(\d{17})'.*disconnected",
                    r"(\d{17}).*disconnected",
                    r"(\d{17}).*left",
                    r"(\d{17}).*quit"
                ]

                for pattern in disconnect_patterns:
                    disconnect_match = re.search(pattern, line, re.IGNORECASE)
                    if disconnect_match:
                        steam_id = disconnect_match.group(1)
                        if steam_id in self.known_players:
                            player_name = self.known_players[steam_id]['name']
                            self.known_players[steam_id]['status'] = 'offline'
                            print(f"Debug: ❌ JOGADOR DESCONECTADO: {player_name} ({steam_id})")
                            self.show_message(f"🔴 {player_name} desconectou do servidor")
                        break

            # Atualizar contadores
            online_count = len([p for p in self.known_players.values() if p['status'] == 'online'])
            if online_count > 0:
                print(f"Debug: 👥 {online_count} jogador(es) online detectado(s)")

        except Exception as e:
            print(f"Erro ao verificar eventos de jogadores: {e}")

    def get_online_players_from_logs(self):
        """Extrai jogadores online dos logs do servidor"""
        try:
            # Tentar múltiplos caminhos para logs
            possible_log_paths = [
                self.logs_path,
                "../logs",
                "logs",
                "../VRisingServer_Data/StreamingAssets/Logs",
                "VRisingServer_Data/StreamingAssets/Logs"
            ]

            logs_path_found = None
            for path in possible_log_paths:
                if os.path.exists(path):
                    logs_path_found = path
                    print(f"Debug: Pasta de logs encontrada: {path}")
                    break

            if not logs_path_found:
                print(f"Debug: Nenhuma pasta de logs encontrada. Tentativas: {possible_log_paths}")
                return []

            # Pegar o log mais recente
            log_files = []
            for file in os.listdir(logs_path_found):
                if file.endswith('.log') or file.endswith('.txt'):
                    log_files.append(file)

            if not log_files:
                print(f"Debug: Nenhum arquivo de log encontrado em: {logs_path_found}")
                return []

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(logs_path_found, f)))
            log_path = os.path.join(logs_path_found, latest_log)
            print(f"Debug: Analisando log: {latest_log}")

            # Dicionário para rastrear jogadores
            players = {}
            import re

            # Ler as últimas linhas do log
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                print(f"Debug: Total de linhas no log: {len(lines)}")

                # Analisar últimas 2000 linhas para encontrar conexões/desconexões
                analyzed_lines = lines[-2000:] if len(lines) > 2000 else lines
                connection_found = False

                for line in analyzed_lines:
                    line = line.strip()

                    # Múltiplos padrões para detectar conexões
                    connection_patterns = [
                        # Padrão específico do V Rising
                        r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected",
                        # Padrões alternativos
                        r"User.*'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                        r"'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                        r"(\d{17}).*Character:\s*'([^']+)'.*connected",
                        # Padrões mais genéricos
                        r"User.*'(\d{17})'.*connected",
                        r"'(\d{17})'.*connected",
                        r"(\d{17}).*connected"
                    ]

                    # Tentar cada padrão
                    for pattern in connection_patterns:
                        match = re.search(pattern, line, re.IGNORECASE)
                        if match:
                            steam_id = match.group(1)

                            # Tentar extrair nome do personagem
                            character_name = "Jogador"
                            if len(match.groups()) >= 2:
                                character_name = match.group(2)
                            else:
                                # Tentar extrair nome de outras formas
                                char_patterns = [
                                    r"Character:\s*'([^']+)'",
                                    r"Character\s*:\s*([^\s,]+)",
                                    r"Name:\s*'([^']+)'",
                                    r"Player:\s*'([^']+)'"
                                ]
                                for char_pattern in char_patterns:
                                    char_match = re.search(char_pattern, line, re.IGNORECASE)
                                    if char_match:
                                        character_name = char_match.group(1)
                                        break

                            connection_found = True

                            # Extrair timestamp do início da linha [HH:MM:SS]
                            time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                            connect_time = time_match.group(1) if time_match else "Agora"

                            players[steam_id] = {
                                'name': character_name,
                                'steam_id': steam_id,
                                'connect_time': connect_time,
                                'status': 'online',
                                'last_seen': line
                            }
                            print(f"Debug: Jogador encontrado: {character_name} ({steam_id}) - Padrão usado: {pattern}")
                            break  # Parar após encontrar com o primeiro padrão

                    # Verificar desconexões
                    disconnect_patterns = [
                        r"User.*'(\d{17})'.*disconnected",
                        r"'(\d{17})'.*disconnected",
                        r"(\d{17}).*disconnected",
                        r"(\d{17}).*left",
                        r"(\d{17}).*quit"
                    ]

                    for pattern in disconnect_patterns:
                        disconnect_match = re.search(pattern, line, re.IGNORECASE)
                        if disconnect_match:
                            steam_id = disconnect_match.group(1)
                            if steam_id in players:
                                players[steam_id]['status'] = 'offline'
                                print(f"Debug: Jogador desconectado: {steam_id}")
                            break

                print(f"Debug: Conexões encontradas: {connection_found}")
                print(f"Debug: Total de jogadores rastreados: {len(players)}")

            # Retornar apenas jogadores online
            online_players = [player for player in players.values() if player['status'] == 'online']
            print(f"Debug: Jogadores online: {len(online_players)}")

            # Se não encontrou jogadores, tentar método alternativo
            if not online_players and self.server_running:
                print("Debug: Tentando método alternativo...")
                online_players = self.get_players_from_server_process()

            return online_players

        except Exception as e:
            print(f"Erro ao analisar logs: {e}")
            return []

    def get_players_alternative_method(self):
        """Método alternativo para detectar jogadores (simulação para teste)"""
        try:
            # Verificar se há conexões de rede ativas na porta do servidor
            import psutil

            if not self.server_running or not self.server_process:
                return []

            # Obter conexões do processo do servidor
            try:
                process = psutil.Process(self.server_process.pid)
                connections = process.net_connections()

                # Contar conexões estabelecidas na porta do servidor
                established_connections = [
                    conn for conn in connections
                    if conn.status == 'ESTABLISHED' and
                    conn.laddr.port == self.config.get('port', 9876)
                ]

                # Simular jogadores baseado nas conexões
                players = []
                for i, conn in enumerate(established_connections):
                    players.append({
                        'name': f'Jogador_{i+1}',
                        'steam_id': f'7656119800000000{i}',
                        'connect_time': 'Ativo',
                        'status': 'online',
                        'ip': conn.raddr.ip if conn.raddr else 'Local'
                    })

                return players

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                return []

        except Exception as e:
            print(f"Erro no método alternativo: {e}")
            return []

    def get_players_from_server_process(self):
        """Detecta jogadores através de conexões de rede do processo do servidor"""
        try:
            if not self.server_running or not self.server_process:
                return []

            import psutil
            process = psutil.Process(self.server_process.pid)
            connections = process.net_connections()

            # Contar conexões estabelecidas na porta do servidor
            server_port = self.config.get('port', 9876)
            established_connections = []

            for conn in connections:
                if (conn.status == 'ESTABLISHED' and
                    conn.laddr and conn.laddr.port == server_port and
                    conn.raddr):
                    established_connections.append(conn)

            # Simular jogadores baseado nas conexões
            players = []
            for i, conn in enumerate(established_connections):
                # Criar um "jogador" baseado na conexão
                fake_steam_id = f"765611980000000{i:02d}"
                players.append({
                    'name': f'Jogador_Conectado_{i+1}',
                    'steam_id': fake_steam_id,
                    'connect_time': 'Ativo',
                    'status': 'online',
                    'ip': conn.raddr.ip if conn.raddr else 'Local',
                    'method': 'network_detection'
                })

            print(f"Debug: Detectados {len(players)} jogadores via conexões de rede")
            return players

        except Exception as e:
            print(f"Erro na detecção por processo: {e}")
            return []

    def refresh_admin_lists(self):
        """Atualiza listas de admins e banidos"""
        try:
            # Atualizar lista de admins
            self.admins_listbox.delete("1.0", "end")
            admin_file = os.path.join(self.data_path, "Settings", "adminlist.txt")

            self.show_message(f"🔍 Verificando arquivo de admins: {admin_file}")

            if os.path.exists(admin_file):
                with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    admins = [line.strip() for line in lines if line.strip()]

                    if admins:
                        self.admins_listbox.insert("end", f"👑 {len(admins)} Admin(s):\n\n")
                        for i, admin in enumerate(admins, 1):
                            # Mostrar apenas os últimos 4 dígitos para privacidade
                            admin_display = f"***{admin[-4:]}" if len(admin) > 4 else admin
                            self.admins_listbox.insert("end", f"{i}. {admin_display}\n")
                            self.admins_listbox.insert("end", f"   ID: {admin}\n\n")
                        self.show_message(f"✅ {len(admins)} administrador(es) carregado(s)")
                    else:
                        self.admins_listbox.insert("end", "❌ Nenhum admin\nconfigurado\n\n")
                        self.admins_listbox.insert("end", "💡 Para adicionar:\n")
                        self.admins_listbox.insert("end", "1. Digite Steam ID\n")
                        self.admins_listbox.insert("end", "2. Clique 'Tornar Admin'")
                        self.show_message("⚠️ Arquivo de admins está vazio")
            else:
                self.admins_listbox.insert("end", "Arquivo adminlist.txt\nnão encontrado")
                self.show_message("⚠️ Arquivo adminlist.txt não encontrado")

            # Atualizar lista de banidos
            self.banned_listbox.delete("1.0", "end")
            ban_file = os.path.join(self.data_path, "Settings", "banlist.txt")

            if os.path.exists(ban_file):
                with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    banned = [line.strip() for line in lines if line.strip()]

                    if banned:
                        self.banned_listbox.insert("end", f"🚫 {len(banned)} Banido(s):\n\n")
                        for i, ban in enumerate(banned, 1):
                            # Mostrar apenas os últimos 4 dígitos para privacidade
                            ban_display = f"***{ban[-4:]}" if len(ban) > 4 else ban
                            self.banned_listbox.insert("end", f"{i}. {ban_display}\n")
                            self.banned_listbox.insert("end", f"   ID: {ban}\n\n")
                    else:
                        self.banned_listbox.insert("end", "✅ Nenhum jogador\nbanido\n\n")
                        self.banned_listbox.insert("end", "💡 Para banir:\n")
                        self.banned_listbox.insert("end", "1. Digite Steam ID\n")
                        self.banned_listbox.insert("end", "2. Clique 'Banir Jogador'")
            else:
                self.banned_listbox.insert("end", "Arquivo banlist.txt\nnão encontrado")

        except Exception as e:
            self.show_message(f"❌ Erro ao atualizar listas: {e}")
            self.admins_listbox.insert("end", f"Erro: {e}")
            self.banned_listbox.insert("end", f"Erro: {e}")

    def make_admin(self):
        """Torna um jogador administrador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            # Criar diretório Settings se não existir
            settings_dir = os.path.join(self.data_path, "Settings")
            os.makedirs(settings_dir, exist_ok=True)

            admin_file = os.path.join(settings_dir, "adminlist.txt")

            # Ler admins existentes
            existing_admins = []
            if os.path.exists(admin_file):
                with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_admins = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se já é admin
            if player_id in existing_admins:
                self.show_message(f"⚠️ {player_id} já é administrador")
                return

            # Adicionar novo admin
            existing_admins.append(player_id)

            # Salvar lista atualizada
            with open(admin_file, 'w', encoding='utf-8') as f:
                for admin in existing_admins:
                    f.write(f"{admin}\n")

            self.show_message(f"👑 {player_id} foi promovido a administrador")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao tornar admin: {e}")

    def remove_admin(self):
        """Remove privilégios de administrador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            admin_file = os.path.join(self.data_path, "Settings", "adminlist.txt")

            if not os.path.exists(admin_file):
                self.show_message("⚠️ Arquivo adminlist.txt não encontrado")
                return

            # Ler admins existentes
            with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                existing_admins = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se é admin
            if player_id not in existing_admins:
                self.show_message(f"⚠️ {player_id} não é administrador")
                return

            # Remover admin
            existing_admins.remove(player_id)

            # Salvar lista atualizada
            with open(admin_file, 'w', encoding='utf-8') as f:
                for admin in existing_admins:
                    f.write(f"{admin}\n")

            self.show_message(f"👤 Privilégios de admin removidos de {player_id}")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao remover admin: {e}")

    def ban_player(self):
        """Bane um jogador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            # Criar diretório Settings se não existir
            settings_dir = os.path.join(self.data_path, "Settings")
            os.makedirs(settings_dir, exist_ok=True)

            ban_file = os.path.join(settings_dir, "banlist.txt")

            # Ler banidos existentes
            existing_banned = []
            if os.path.exists(ban_file):
                with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_banned = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se já está banido
            if player_id in existing_banned:
                self.show_message(f"⚠️ {player_id} já está banido")
                return

            # Adicionar à lista de banidos
            existing_banned.append(player_id)

            # Salvar lista atualizada
            with open(ban_file, 'w', encoding='utf-8') as f:
                for banned in existing_banned:
                    f.write(f"{banned}\n")

            self.show_message(f"🚫 {player_id} foi banido do servidor")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao banir jogador: {e}")

    def unban_player(self):
        """Remove ban de um jogador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            ban_file = os.path.join(self.data_path, "Settings", "banlist.txt")

            if not os.path.exists(ban_file):
                self.show_message("⚠️ Arquivo banlist.txt não encontrado")
                return

            # Ler banidos existentes
            with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                existing_banned = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se está banido
            if player_id not in existing_banned:
                self.show_message(f"⚠️ {player_id} não está banido")
                return

            # Remover da lista de banidos
            existing_banned.remove(player_id)

            # Salvar lista atualizada
            with open(ban_file, 'w', encoding='utf-8') as f:
                for banned in existing_banned:
                    f.write(f"{banned}\n")

            self.show_message(f"✅ Ban removido de {player_id}")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao desbanir jogador: {e}")

    def refresh_mods(self):
        """Atualiza lista de mods instalados"""
        try:
            self.mods_listbox.delete("1.0", "end")

            # Verificar pasta BepInEx/plugins
            mods_path = "../BepInEx/plugins"
            if not os.path.exists(mods_path):
                # Tentar caminho alternativo
                mods_path = "BepInEx/plugins"
                if not os.path.exists(mods_path):
                    self.mods_listbox.insert("end", "📁 Pasta de mods não encontrada\n\n")
                    self.mods_listbox.insert("end", "💡 Para usar mods:\n")
                    self.mods_listbox.insert("end", "1. Instale BepInEx\n")
                    self.mods_listbox.insert("end", "2. Crie pasta BepInEx/plugins\n")
                    self.mods_listbox.insert("end", "3. Adicione mods na pasta\n\n")
                    self.mods_listbox.insert("end", "🌐 Sites de mods:\n")
                    self.mods_listbox.insert("end", "• thunderstore.io\n")
                    self.mods_listbox.insert("end", "• nexusmods.com\n")
                    return

            # Escanear mods
            mods_found = []

            for item in os.listdir(mods_path):
                item_path = os.path.join(mods_path, item)

                if os.path.isfile(item_path) and item.endswith('.dll'):
                    # Arquivo DLL (mod)
                    size = os.path.getsize(item_path) / 1024  # KB
                    mods_found.append({
                        'name': item,
                        'type': 'DLL Plugin',
                        'size': f"{size:.1f} KB",
                        'path': item_path,
                        'enabled': True  # Assumir ativo se está na pasta
                    })
                elif os.path.isdir(item_path):
                    # Pasta de mod
                    dll_files = [f for f in os.listdir(item_path) if f.endswith('.dll')]
                    if dll_files:
                        mods_found.append({
                            'name': item,
                            'type': 'Mod Folder',
                            'size': f"{len(dll_files)} arquivo(s)",
                            'path': item_path,
                            'enabled': True
                        })

            if mods_found:
                self.mods_listbox.insert("end", f"📦 {len(mods_found)} Mod(s) Encontrado(s):\n\n")

                for i, mod in enumerate(mods_found, 1):
                    status = "✅" if mod['enabled'] else "❌"
                    self.mods_listbox.insert("end", f"{i}. {status} {mod['name']}\n")
                    self.mods_listbox.insert("end", f"   📁 Tipo: {mod['type']}\n")
                    self.mods_listbox.insert("end", f"   📏 Tamanho: {mod['size']}\n")
                    self.mods_listbox.insert("end", f"   📂 Caminho: {mod['path']}\n\n")
            else:
                self.mods_listbox.insert("end", "📦 Nenhum mod encontrado\n\n")
                self.mods_listbox.insert("end", "💡 Para adicionar mods:\n")
                self.mods_listbox.insert("end", "1. Baixe mods de sites confiáveis\n")
                self.mods_listbox.insert("end", "2. Extraia na pasta BepInEx/plugins\n")
                self.mods_listbox.insert("end", "3. Reinicie o servidor\n")

            self.show_message(f"🔄 Lista de mods atualizada: {len(mods_found)} encontrado(s)")

        except Exception as e:
            self.mods_listbox.insert("end", f"❌ Erro ao listar mods: {e}\n")
            self.show_message(f"❌ Erro ao atualizar mods: {e}")

    def scan_mods(self):
        """Escaneia e valida mods instalados"""
        try:
            self.show_message("🔍 Escaneando mods...")

            # Atualizar informações no painel direito
            self.mod_info_text.delete("1.0", "end")
            self.mod_info_text.insert("end", "🔍 ESCANEAMENTO DE MODS\n")
            self.mod_info_text.insert("end", "=" * 30 + "\n\n")

            # Verificar BepInEx
            bepinex_path = "../BepInEx"
            if not os.path.exists(bepinex_path):
                bepinex_path = "BepInEx"

            if os.path.exists(bepinex_path):
                self.mod_info_text.insert("end", "✅ BepInEx encontrado\n")

                # Verificar estrutura do BepInEx
                config_path = os.path.join(bepinex_path, "config")
                plugins_path = os.path.join(bepinex_path, "plugins")
                core_path = os.path.join(bepinex_path, "core")

                if os.path.exists(config_path):
                    self.mod_info_text.insert("end", "✅ Pasta config encontrada\n")

                    # Verificar arquivos de config importantes
                    bepinex_cfg = os.path.join(config_path, "BepInEx.cfg")
                    if os.path.exists(bepinex_cfg):
                        self.mod_info_text.insert("end", "✅ BepInEx.cfg encontrado\n")
                    else:
                        self.mod_info_text.insert("end", "⚠️ BepInEx.cfg não encontrado\n")
                        self.mod_info_text.insert("end", "🔧 Criando BepInEx.cfg padrão...\n")
                        self.create_default_bepinex_config(bepinex_cfg)
                else:
                    self.mod_info_text.insert("end", "⚠️ Pasta config não encontrada\n")
                    self.mod_info_text.insert("end", "💡 Execute o servidor uma vez para criar\n")

                if os.path.exists(plugins_path):
                    self.mod_info_text.insert("end", "✅ Pasta plugins encontrada\n")
                else:
                    self.mod_info_text.insert("end", "⚠️ Pasta plugins não encontrada\n")

                if os.path.exists(core_path):
                    self.mod_info_text.insert("end", "✅ Pasta core encontrada\n")
                else:
                    self.mod_info_text.insert("end", "⚠️ Pasta core não encontrada\n")
            else:
                self.mod_info_text.insert("end", "❌ BepInEx não instalado\n")
                self.mod_info_text.insert("end", "\n💡 Para instalar BepInEx:\n")
                self.mod_info_text.insert("end", "1. Baixe de github.com/BepInEx\n")
                self.mod_info_text.insert("end", "2. Extraia na pasta do servidor\n")
                self.mod_info_text.insert("end", "3. Execute o servidor uma vez\n")

            # Atualizar lista
            self.refresh_mods()

        except Exception as e:
            self.show_message(f"❌ Erro no escaneamento: {e}")

    def create_default_bepinex_config(self, config_path):
        """Cria arquivo de configuração padrão do BepInEx"""
        try:
            # Criar diretório se não existir
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            # Configuração padrão do BepInEx
            default_config = """[Caching]

## Enable/disable assembly metadata cache
## Enabling this will speed up discovery of plugins and patchers by caching the metadata of all types BepInEx discovers.
# Setting type: Boolean
# Default value: true
EnableAssemblyCache = true

[Logging]

## Enables or disables logging to disk.
# Setting type: Boolean
# Default value: true
DiskLogging = true

## Enables or disables logging to console.
# Setting type: Boolean
# Default value: false
ConsoleLogging = false

## Enables or disables logging to Unity's built-in logging system.
# Setting type: Boolean
# Default value: true
UnityLogging = true

[Logging.Console]

## Enables or disables logging to console.
# Setting type: Boolean
# Default value: false
Enabled = false

[Logging.Disk]

## Include unity log messages in log file output.
# Setting type: Boolean
# Default value: false
WriteUnityLog = false

## Appends to the log file instead of overwriting, on game startup.
# Setting type: Boolean
# Default value: false
AppendLog = false

## Enables or disables immediate log flushing. Enable if you want to see logs appear immediately. Disable if you want to improve performance.
# Setting type: Boolean
# Default value: false
InstantFlushing = false

[Preloader]

## Enables or disables the BepInEx preloader.
# Setting type: Boolean
# Default value: true
Enabled = true

[Preloader.Entrypoint]

## The local filename of the assembly to target.
# Setting type: String
# Default value: UnityEngine.CoreModule.dll
Assembly = UnityEngine.CoreModule.dll

## The name of the type in the entrypoint assembly to search for the entrypoint method.
# Setting type: String
# Default value: Application
Type = Application

## The name of the method in the specified entrypoint assembly and type to hook and load Chainloader from.
# Setting type: String
# Default value: .cctor
Method = .cctor
"""

            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(default_config)

            self.mod_info_text.insert("end", "✅ BepInEx.cfg criado com sucesso\n")
            self.show_message("✅ Arquivo BepInEx.cfg criado com configurações padrão")

        except Exception as e:
            error_msg = str(e)
            self.mod_info_text.insert("end", f"❌ Erro ao criar config: {error_msg}\n")
            self.show_message(f"❌ Erro ao criar BepInEx.cfg: {error_msg}")

    def install_mod(self):
        """Instala um mod a partir de URL ou arquivo"""
        url_or_path = self.mod_url_entry.get().strip()
        if not url_or_path:
            self.show_message("⚠️ Digite uma URL ou caminho do mod")
            return

        self.show_message(f"📥 Iniciando instalação de: {url_or_path}")

        # Executar instalação em thread separada para não travar a interface
        def install_thread():
            try:
                if url_or_path.startswith('http'):
                    self.install_from_url(url_or_path)
                else:
                    self.install_from_file(url_or_path)
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: self.show_message(f"❌ Erro na instalação: {error_msg}"))

        threading.Thread(target=install_thread, daemon=True).start()

    def install_from_url(self, url):
        """Instala mod a partir de URL"""
        import urllib.request
        import tempfile

        try:
            # Determinar se é BepInEx ou mod regular
            if 'BepInEx' in url and 'github.com' in url:
                self.root.after(0, lambda: self.show_message("🔧 Detectado: BepInEx Framework"))
                self.install_bepinex_from_url(url)
            else:
                self.root.after(0, lambda: self.show_message("📦 Detectado: Mod regular"))
                self.install_regular_mod_from_url(url)

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.show_message(f"❌ Erro no download: {error_msg}"))

    def install_bepinex_from_url(self, url):
        """Instala BepInEx a partir de URL do GitHub"""
        import urllib.request
        import tempfile
        import zipfile
        import shutil

        try:
            self.root.after(0, lambda: self.show_message("� Baixando BepInEx..."))

            # Baixar arquivo
            with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as tmp_file:
                urllib.request.urlretrieve(url, tmp_file.name)

                self.root.after(0, lambda: self.show_message("📦 Extraindo BepInEx..."))

                # Extrair para pasta temporária
                with tempfile.TemporaryDirectory() as temp_dir:
                    with zipfile.ZipFile(tmp_file.name, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)

                    # Encontrar pasta BepInEx extraída
                    bepinex_source = None
                    for item in os.listdir(temp_dir):
                        item_path = os.path.join(temp_dir, item)
                        if os.path.isdir(item_path) and 'BepInEx' in item:
                            bepinex_source = item_path
                            break

                    if not bepinex_source:
                        # BepInEx pode estar na raiz
                        if os.path.exists(os.path.join(temp_dir, 'BepInEx')):
                            bepinex_source = temp_dir

                    if bepinex_source:
                        # Copiar para pasta do servidor
                        server_path = ".." if os.path.exists("../VRisingServer.exe") else "."
                        bepinex_dest = os.path.join(server_path, "BepInEx")

                        self.root.after(0, lambda: self.show_message("📁 Instalando na pasta do servidor..."))

                        # Copiar BepInEx
                        if os.path.exists(os.path.join(bepinex_source, "BepInEx")):
                            shutil.copytree(os.path.join(bepinex_source, "BepInEx"), bepinex_dest, dirs_exist_ok=True)

                        # Copiar outros arquivos (winhttp.dll, etc.)
                        for file in os.listdir(bepinex_source):
                            file_path = os.path.join(bepinex_source, file)
                            if os.path.isfile(file_path):
                                shutil.copy2(file_path, server_path)

                        self.root.after(0, lambda: self.show_message("✅ BepInEx instalado com sucesso!"))
                        self.root.after(0, lambda: self.show_message("💡 Reinicie o servidor para ativar o BepInEx"))
                        self.root.after(0, self.refresh_mods)
                    else:
                        self.root.after(0, lambda: self.show_message("❌ Estrutura do BepInEx não reconhecida"))

                # Limpar arquivo temporário
                os.unlink(tmp_file.name)

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.show_message(f"❌ Erro ao instalar BepInEx: {error_msg}"))

    def install_regular_mod_from_url(self, url):
        """Instala mod regular a partir de URL"""
        import urllib.request
        import tempfile
        import zipfile
        import shutil

        try:
            self.root.after(0, lambda: self.show_message(f"� Baixando mod: {url}"))

            # Verificar se pasta plugins existe
            plugins_path = "../BepInEx/plugins" if os.path.exists("../BepInEx") else "BepInEx/plugins"
            if not os.path.exists(plugins_path):
                self.root.after(0, lambda: self.show_message("❌ Pasta BepInEx/plugins não encontrada"))
                self.root.after(0, lambda: self.show_message("💡 Instale o BepInEx primeiro"))
                return

            # Baixar arquivo
            with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as tmp_file:
                urllib.request.urlretrieve(url, tmp_file.name)

                self.root.after(0, lambda: self.show_message("📦 Extraindo mod..."))

                # Extrair para pasta temporária
                with tempfile.TemporaryDirectory() as temp_dir:
                    with zipfile.ZipFile(tmp_file.name, 'r') as zip_ref:
                        zip_ref.extractall(temp_dir)

                    # Procurar arquivos .dll (mods)
                    dll_files = []
                    for root, _, files in os.walk(temp_dir):
                        for file in files:
                            if file.endswith('.dll'):
                                dll_files.append(os.path.join(root, file))

                    if dll_files:
                        self.root.after(0, lambda: self.show_message(f"� Encontrados {len(dll_files)} arquivo(s) de mod"))

                        # Copiar arquivos .dll para plugins
                        for dll_file in dll_files:
                            dll_name = os.path.basename(dll_file)
                            dest_path = os.path.join(plugins_path, dll_name)
                            shutil.copy2(dll_file, dest_path)
                            self.root.after(0, lambda name=dll_name: self.show_message(f"✅ Instalado: {name}"))

                        # Procurar e copiar outros arquivos importantes
                        other_files = []
                        for root, _, files in os.walk(temp_dir):
                            for file in files:
                                if file.endswith(('.cfg', '.json', '.txt')) and not file.startswith('manifest'):
                                    other_files.append(os.path.join(root, file))

                        if other_files:
                            config_path = "../BepInEx/config" if os.path.exists("../BepInEx") else "BepInEx/config"
                            os.makedirs(config_path, exist_ok=True)

                            for config_file in other_files:
                                config_name = os.path.basename(config_file)
                                dest_path = os.path.join(config_path, config_name)
                                shutil.copy2(config_file, dest_path)
                                self.root.after(0, lambda name=config_name: self.show_message(f"📝 Config: {name}"))

                        self.root.after(0, lambda: self.show_message("✅ Mod instalado com sucesso!"))
                        self.root.after(0, lambda: self.show_message("💡 Reinicie o servidor para ativar o mod"))
                        self.root.after(0, self.refresh_mods)

                    else:
                        self.root.after(0, lambda: self.show_message("❌ Nenhum arquivo .dll encontrado no mod"))
                        self.root.after(0, lambda: self.show_message("💡 Verifique se é um mod válido para V Rising"))

                # Limpar arquivo temporário
                os.unlink(tmp_file.name)

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self.show_message(f"❌ Erro ao instalar mod: {error_msg}"))

    def install_from_file(self, file_path):
        """Instala mod a partir de arquivo local"""
        if not os.path.exists(file_path):
            self.root.after(0, lambda: self.show_message("❌ Arquivo não encontrado"))
            return

        self.root.after(0, lambda: self.show_message("🚧 Instalação de arquivos locais em desenvolvimento"))
        self.root.after(0, lambda: self.show_message("💡 Por enquanto, copie manualmente para BepInEx/plugins"))

    def remove_mod(self):
        """Remove um mod selecionado"""
        self.show_message("🚧 Funcionalidade de remoção em desenvolvimento")
        self.show_message("💡 Por enquanto, remova mods manualmente da pasta BepInEx/plugins")

    def enable_mod(self):
        """Ativa um mod"""
        self.show_message("🚧 Funcionalidade de ativação em desenvolvimento")

    def disable_mod(self):
        """Desativa um mod"""
        self.show_message("🚧 Funcionalidade de desativação em desenvolvimento")

    def generate_charts(self):
        """Gera gráficos de performance do servidor usando tkinter Canvas"""
        try:
            import tkinter as tk
            import datetime
            import random

            # Limpar container anterior
            for widget in self.charts_container.winfo_children():
                widget.destroy()

            # Coletar dados em tempo real
            current_time = datetime.datetime.now()
            online_players = len([p for p in self.known_players.values() if p['status'] == 'online'])

            # Obter estatísticas do sistema se possível
            cpu_usage = 0
            memory_usage = 0

            try:
                if self.server_running and self.server_process:
                    import psutil
                    process = psutil.Process(self.server_process.pid)
                    cpu_usage = process.cpu_percent()
                    memory_info = process.memory_info()
                    memory_usage = memory_info.rss // (1024 * 1024)  # MB
            except:
                # Simular dados se não conseguir obter reais
                cpu_usage = random.randint(20, 80) if self.server_running else 0
                memory_usage = random.randint(1000, 3000) if self.server_running else 0

            # Atualizar dados históricos
            if len(self.server_stats['timestamps']) >= 24:
                # Remover dados antigos
                self.server_stats['timestamps'].pop(0)
                self.server_stats['player_count'].pop(0)
                self.server_stats['cpu_usage'].pop(0)
                self.server_stats['memory_usage'].pop(0)

            # Adicionar dados atuais
            self.server_stats['timestamps'].append(current_time.strftime("%H:%M"))
            self.server_stats['player_count'].append(online_players)
            self.server_stats['cpu_usage'].append(cpu_usage)
            self.server_stats['memory_usage'].append(memory_usage)

            # Criar frame para gráficos
            charts_grid = ctk.CTkFrame(self.charts_container)
            charts_grid.pack(fill="both", expand=True, padx=10, pady=10)

            # Configurar grid
            charts_grid.grid_columnconfigure(0, weight=1)
            charts_grid.grid_columnconfigure(1, weight=1)
            charts_grid.grid_rowconfigure(0, weight=1)
            charts_grid.grid_rowconfigure(1, weight=1)

            # Gráfico 1: Jogadores Online (Linha)
            self.create_line_chart(charts_grid, "👥 Jogadores Online",
                                 self.server_stats['timestamps'][-12:],
                                 self.server_stats['player_count'][-12:],
                                 "#1f77b4", 0, 0)

            # Gráfico 2: CPU Usage (Barras)
            self.create_bar_chart(charts_grid, "💻 Uso de CPU (%)",
                                self.server_stats['cpu_usage'][-6:],
                                "#ff7f0e", 0, 1)

            # Gráfico 3: Memória (Linha)
            self.create_line_chart(charts_grid, "🧠 RAM (MB)",
                                 self.server_stats['timestamps'][-12:],
                                 self.server_stats['memory_usage'][-12:],
                                 "#2ca02c", 1, 0)

            # Gráfico 4: Status do Servidor (Pizza)
            self.create_pie_chart(charts_grid, "🎮 Status dos Slots",
                                online_players, 40, 1, 1)

            self.show_message("� Gráficos atualizados com estatísticas em tempo real!")

        except Exception as e:
            self.show_message(f"❌ Erro ao gerar gráficos: {e}")
            print(f"Erro detalhado: {e}")

    def create_line_chart(self, parent, title, x_data, y_data, color, row, col):
        """Cria um gráfico de linha usando Canvas"""
        frame = ctk.CTkFrame(parent)
        frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

        # Título
        title_label = ctk.CTkLabel(frame, text=title, font=ctk.CTkFont(size=12, weight="bold"))
        title_label.pack(pady=(5, 0))

        # Canvas para o gráfico
        canvas = tk.Canvas(frame, width=300, height=200, bg='#1e1e1e', highlightthickness=0)
        canvas.pack(padx=10, pady=10)

        if not y_data or len(y_data) == 0:
            canvas.create_text(150, 100, text="Sem dados", fill="white", font=("Arial", 12))
            return

        # Configurações do gráfico
        margin = 30
        width = 300 - 2 * margin
        height = 200 - 2 * margin

        max_y = max(y_data) if max(y_data) > 0 else 1
        min_y = min(y_data)

        # Desenhar eixos
        canvas.create_line(margin, height + margin, width + margin, height + margin, fill="white", width=2)  # X
        canvas.create_line(margin, margin, margin, height + margin, fill="white", width=2)  # Y

        # Desenhar pontos e linhas
        points = []
        for i, y_val in enumerate(y_data):
            x = margin + (i * width / (len(y_data) - 1)) if len(y_data) > 1 else margin + width/2
            y = margin + height - ((y_val - min_y) * height / (max_y - min_y)) if max_y != min_y else margin + height/2
            points.extend([x, y])

            # Desenhar ponto
            canvas.create_oval(x-3, y-3, x+3, y+3, fill=color, outline=color)

        # Desenhar linha conectando os pontos
        if len(points) >= 4:
            canvas.create_line(points, fill=color, width=2, smooth=True)

        # Labels dos valores
        canvas.create_text(margin//2, margin, text=str(int(max_y)), fill="white", font=("Arial", 8))
        canvas.create_text(margin//2, height + margin, text=str(int(min_y)), fill="white", font=("Arial", 8))

    def create_bar_chart(self, parent, title, data, color, row, col):
        """Cria um gráfico de barras usando Canvas"""
        frame = ctk.CTkFrame(parent)
        frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

        # Título
        title_label = ctk.CTkLabel(frame, text=title, font=ctk.CTkFont(size=12, weight="bold"))
        title_label.pack(pady=(5, 0))

        # Canvas para o gráfico
        canvas = tk.Canvas(frame, width=300, height=200, bg='#1e1e1e', highlightthickness=0)
        canvas.pack(padx=10, pady=10)

        if not data or len(data) == 0:
            canvas.create_text(150, 100, text="Sem dados", fill="white", font=("Arial", 12))
            return

        # Configurações do gráfico
        margin = 30
        width = 300 - 2 * margin
        height = 200 - 2 * margin

        max_val = max(data) if max(data) > 0 else 1
        bar_width = width / len(data)

        # Desenhar eixos
        canvas.create_line(margin, height + margin, width + margin, height + margin, fill="white", width=2)
        canvas.create_line(margin, margin, margin, height + margin, fill="white", width=2)

        # Desenhar barras
        for i, val in enumerate(data):
            x1 = margin + i * bar_width + 5
            x2 = margin + (i + 1) * bar_width - 5
            y1 = height + margin
            y2 = margin + height - (val * height / max_val)

            canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline=color)

            # Valor no topo da barra
            canvas.create_text((x1 + x2) / 2, y2 - 10, text=str(int(val)), fill="white", font=("Arial", 8))

        # Label do máximo
        canvas.create_text(margin//2, margin, text=str(int(max_val)), fill="white", font=("Arial", 8))

    def create_pie_chart(self, parent, title, online, total, row, col):
        """Cria um gráfico de pizza usando Canvas"""
        frame = ctk.CTkFrame(parent)
        frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

        # Título
        title_label = ctk.CTkLabel(frame, text=title, font=ctk.CTkFont(size=12, weight="bold"))
        title_label.pack(pady=(5, 0))

        # Canvas para o gráfico
        canvas = tk.Canvas(frame, width=300, height=200, bg='#1e1e1e', highlightthickness=0)
        canvas.pack(padx=10, pady=10)

        # Centro e raio
        cx, cy = 150, 100
        radius = 60

        if online == 0:
            # Círculo vazio
            canvas.create_oval(cx-radius, cy-radius, cx+radius, cy+radius,
                             fill="#d62728", outline="white", width=2)
            canvas.create_text(cx, cy, text="Vazio", fill="white", font=("Arial", 12, "bold"))
        else:
            # Calcular ângulos
            online_angle = (online / total) * 360
            offline_angle = 360 - online_angle

            # Setor online (verde)
            canvas.create_arc(cx-radius, cy-radius, cx+radius, cy+radius,
                            start=0, extent=online_angle, fill="#2ca02c", outline="white", width=2)

            # Setor offline (vermelho)
            canvas.create_arc(cx-radius, cy-radius, cx+radius, cy+radius,
                            start=online_angle, extent=offline_angle, fill="#d62728", outline="white", width=2)

            # Texto central
            canvas.create_text(cx, cy-10, text=f"{online}/{total}", fill="white", font=("Arial", 14, "bold"))
            canvas.create_text(cx, cy+10, text="Online", fill="white", font=("Arial", 10))

        # Legenda
        canvas.create_rectangle(50, 170, 60, 180, fill="#2ca02c", outline="white")
        canvas.create_text(70, 175, text=f"Online: {online}", fill="white", font=("Arial", 9), anchor="w")

        canvas.create_rectangle(150, 170, 160, 180, fill="#d62728", outline="white")
        canvas.create_text(170, 175, text=f"Livre: {total-online}", fill="white", font=("Arial", 9), anchor="w")

    def debug_player_detection(self):
        """Debug detalhado da detecção de jogadores"""
        try:
            # Criar janela de debug
            debug_window = ctk.CTkToplevel(self.root)
            debug_window.title("🔍 Debug - Detecção de Jogadores")
            debug_window.geometry("900x700")

            # Área de texto para debug
            debug_text = ctk.CTkTextbox(debug_window, height=600, wrap="word")
            debug_text.pack(fill="both", expand=True, padx=10, pady=10)

            debug_text.insert("end", "🔍 DEBUG DETALHADO - DETECÇÃO DE JOGADORES\n")
            debug_text.insert("end", "=" * 60 + "\n\n")

            # 1. Verificar caminhos de logs
            debug_text.insert("end", "📁 VERIFICAÇÃO DE CAMINHOS:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                exists = os.path.exists(path)
                debug_text.insert("end", f"• {path}: {'✅ EXISTE' if exists else '❌ NÃO EXISTE'}\n")
                if exists and not log_path_found:
                    log_path_found = path

            debug_text.insert("end", f"\n🎯 Caminho escolhido: {log_path_found}\n\n")

            if not log_path_found:
                debug_text.insert("end", "❌ PROBLEMA: Nenhuma pasta de logs encontrada!\n")
                debug_text.insert("end", "💡 SOLUÇÃO: Inicie o servidor para gerar logs\n")
                return

            # 2. Listar arquivos de log
            debug_text.insert("end", "📋 ARQUIVOS DE LOG:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            log_files = [f for f in os.listdir(log_path_found) if f.endswith('.log')]
            debug_text.insert("end", f"Total de arquivos .log: {len(log_files)}\n")

            for log_file in log_files[-5:]:  # Últimos 5 arquivos
                log_file_path = os.path.join(log_path_found, log_file)
                size = os.path.getsize(log_file_path)
                mtime = os.path.getmtime(log_file_path)
                import datetime
                mod_time = datetime.datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                debug_text.insert("end", f"• {log_file} ({size} bytes, {mod_time})\n")

            if not log_files:
                debug_text.insert("end", "❌ PROBLEMA: Nenhum arquivo .log encontrado!\n")
                return

            # 3. Analisar log mais recente
            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_path_found, f)))
            log_file_path = os.path.join(log_path_found, latest_log)

            debug_text.insert("end", f"\n📄 ANALISANDO: {latest_log}\n")
            debug_text.insert("end", "-" * 30 + "\n")

            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            lines = content.split('\n')
            debug_text.insert("end", f"Total de linhas: {len(lines)}\n")

            # 4. Procurar por padrões específicos
            debug_text.insert("end", "\n🔍 PROCURANDO PADRÕES:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            import re

            # Padrões a testar
            patterns = [
                (r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected", "Padrão V Rising completo"),
                (r"'(\d{17})'.*Character:\s*'([^']+)'.*connected", "Padrão alternativo 1"),
                (r"(\d{17}).*Character:\s*'([^']+)'.*connected", "Padrão alternativo 2"),
                (r"User.*connected", "Qualquer conexão de usuário"),
                (r"connected", "Qualquer conexão"),
                (r"(\d{17})", "Qualquer Steam ID")
            ]

            for pattern, description in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                debug_text.insert("end", f"• {description}: {len(matches)} matches\n")

                if matches and len(matches) <= 5:  # Mostrar até 5 exemplos
                    for match in matches[-3:]:  # Últimos 3
                        debug_text.insert("end", f"  └─ {match}\n")

            # 5. Mostrar últimas linhas relevantes
            debug_text.insert("end", "\n📝 ÚLTIMAS LINHAS RELEVANTES:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            keywords = ['user', 'player', 'connect', 'character', 'steam', 'join', 'login']
            relevant_lines = []

            for i, line in enumerate(lines[-100:], len(lines)-99):  # Últimas 100 linhas
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in keywords):
                    relevant_lines.append(f"Linha {i}: {line.strip()}")

            if relevant_lines:
                for line in relevant_lines[-10:]:  # Últimas 10 linhas relevantes
                    debug_text.insert("end", f"{line}\n")
            else:
                debug_text.insert("end", "❌ Nenhuma linha relevante encontrada\n")

            # 6. Testar função atual
            debug_text.insert("end", "\n🧪 TESTE DA FUNÇÃO ATUAL:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            self.check_player_events()
            online_count = len([p for p in self.known_players.values() if p['status'] == 'online'])

            debug_text.insert("end", f"Jogadores detectados pela função: {online_count}\n")
            debug_text.insert("end", f"Jogadores conhecidos: {len(self.known_players)}\n")

            if self.known_players:
                debug_text.insert("end", "\nJogadores na memória:\n")
                for steam_id, player in self.known_players.items():
                    debug_text.insert("end", f"• {player['name']} ({steam_id}) - {player['status']}\n")

            # 7. Sugestões
            debug_text.insert("end", "\n💡 SUGESTÕES:\n")
            debug_text.insert("end", "-" * 30 + "\n")
            debug_text.insert("end", "1. Conecte-se ao servidor pelo jogo\n")
            debug_text.insert("end", "2. Aguarde alguns segundos\n")
            debug_text.insert("end", "3. Execute este debug novamente\n")
            debug_text.insert("end", "4. Verifique se o servidor está gerando logs\n")

        except Exception as e:
            debug_text.insert("end", f"\n❌ ERRO NO DEBUG: {e}\n")

    def force_scan_logs(self):
        """Força um scan completo dos logs para detectar jogadores"""
        try:
            self.show_message("⚡ Iniciando scan forçado dos logs...")

            # Resetar posição do arquivo para forçar leitura completa
            if hasattr(self, 'last_log_size'):
                delattr(self, 'last_log_size')

            # Limpar jogadores conhecidos
            self.known_players.clear()

            # Encontrar logs
            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                if os.path.exists(path):
                    log_path_found = path
                    self.show_message(f"📁 Encontrada pasta de logs: {path}")
                    break

            if not log_path_found:
                self.show_message("❌ Nenhuma pasta de logs encontrada!")
                return

            # Encontrar arquivos de log
            log_files = [f for f in os.listdir(log_path_found) if f.endswith('.log')]
            if not log_files:
                self.show_message("❌ Nenhum arquivo .log encontrado!")
                return

            self.show_message(f"📋 Encontrados {len(log_files)} arquivo(s) de log")

            # Analisar todos os logs, começando pelo mais recente
            log_files.sort(key=lambda f: os.path.getmtime(os.path.join(log_path_found, f)), reverse=True)

            total_lines_processed = 0
            connections_found = 0

            for log_file in log_files[:3]:  # Últimos 3 arquivos
                log_file_path = os.path.join(log_path_found, log_file)
                self.show_message(f"🔍 Analisando: {log_file}")

                try:
                    with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    lines = content.split('\n')
                    total_lines_processed += len(lines)

                    import re

                    # Procurar por todas as conexões no arquivo
                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue

                        # Padrões de conexão
                        connection_patterns = [
                            r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected",
                            r"'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                            r"(\d{17}).*Character:\s*'([^']+)'.*connected",
                            r"User.*'(\d{17})'.*connected",
                            r"(\d{17}).*connected"
                        ]

                        for pattern in connection_patterns:
                            connect_match = re.search(pattern, line, re.IGNORECASE)
                            if connect_match:
                                steam_id = connect_match.group(1)

                                # Extrair nome
                                character_name = f"Jogador_{steam_id[-4:]}"
                                if len(connect_match.groups()) > 1 and connect_match.group(2):
                                    character_name = connect_match.group(2)

                                # Extrair timestamp
                                time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                                connect_time = time_match.group(1) if time_match else "Desconhecido"

                                # Adicionar jogador
                                self.known_players[steam_id] = {
                                    'name': character_name,
                                    'steam_id': steam_id,
                                    'connect_time': connect_time,
                                    'status': 'online'
                                }

                                connections_found += 1
                                self.show_message(f"✅ Encontrado: {character_name} ({steam_id[-4:]})")
                                break

                        # Verificar desconexões
                        disconnect_patterns = [
                            r"User.*'(\d{17})'.*disconnected",
                            r"'(\d{17})'.*disconnected",
                            r"(\d{17}).*disconnected"
                        ]

                        for pattern in disconnect_patterns:
                            disconnect_match = re.search(pattern, line, re.IGNORECASE)
                            if disconnect_match:
                                steam_id = disconnect_match.group(1)
                                if steam_id in self.known_players:
                                    self.known_players[steam_id]['status'] = 'offline'
                                    self.show_message(f"❌ Desconectado: {steam_id[-4:]}")
                                break

                except Exception as e:
                    self.show_message(f"❌ Erro ao ler {log_file}: {e}")

            # Resultados
            online_count = len([p for p in self.known_players.values() if p['status'] == 'online'])
            total_known = len(self.known_players)

            self.show_message(f"📊 Scan concluído:")
            self.show_message(f"   • {total_lines_processed} linhas processadas")
            self.show_message(f"   • {connections_found} conexões encontradas")
            self.show_message(f"   • {total_known} jogadores únicos")
            self.show_message(f"   • {online_count} jogadores online")

            if online_count > 0:
                self.show_message("👥 Jogadores online detectados:")
                for steam_id, player in self.known_players.items():
                    if player['status'] == 'online':
                        self.show_message(f"   🎮 {player['name']} ({steam_id[-4:]})")

            # Atualizar lista
            self.refresh_players()

        except Exception as e:
            self.show_message(f"❌ Erro no scan forçado: {e}")

    def start_log_monitoring(self):
        """Inicia o monitoramento em tempo real dos arquivos de log"""
        try:
            # Encontrar pasta de logs
            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                if os.path.exists(path):
                    log_path_found = path
                    break

            if not log_path_found:
                print("Debug: Nenhuma pasta de logs encontrada para monitoramento")
                return

            # Criar handler e observer
            self.log_handler = LogFileHandler(self)
            self.log_observer = Observer()
            self.log_observer.schedule(self.log_handler, log_path_found, recursive=False)

            # Iniciar monitoramento
            self.log_observer.start()
            print(f"Debug: ✅ Monitoramento em tempo real iniciado em: {log_path_found}")

        except Exception as e:
            print(f"Erro ao iniciar monitoramento de logs: {e}")

    def stop_log_monitoring(self):
        """Para o monitoramento em tempo real dos arquivos de log"""
        try:
            if self.log_observer and self.log_observer.is_alive():
                self.log_observer.stop()
                self.log_observer.join()
                print("Debug: ✅ Monitoramento em tempo real parado")
        except Exception as e:
            print(f"Erro ao parar monitoramento de logs: {e}")

    def on_player_connected(self, steam_id, character_name, connect_time, log_path):
        """Callback chamado quando um jogador se conecta"""
        try:
            # Adicionar/atualizar jogador
            self.known_players[steam_id] = {
                'name': character_name,
                'steam_id': steam_id,
                'connect_time': connect_time,
                'status': 'online'
            }

            # Notificar na interface (thread-safe)
            self.root.after(0, lambda: self.show_message(f"🟢 {character_name} conectou ao servidor"))
            self.root.after(0, self.refresh_players)

            print(f"Debug: ✅ JOGADOR CONECTADO: {character_name} ({steam_id[-4:]})")

        except Exception as e:
            print(f"Erro ao processar conexão de jogador: {e}")

    def on_player_disconnected(self, steam_id, log_path):
        """Callback chamado quando um jogador se desconecta"""
        try:
            if steam_id in self.known_players:
                player_name = self.known_players[steam_id]['name']
                self.known_players[steam_id]['status'] = 'offline'

                # Notificar na interface (thread-safe)
                self.root.after(0, lambda: self.show_message(f"🔴 {player_name} desconectou do servidor"))
                self.root.after(0, self.refresh_players)

                print(f"Debug: ❌ JOGADOR DESCONECTADO: {player_name} ({steam_id[-4:]})")

        except Exception as e:
            print(f"Erro ao processar desconexão de jogador: {e}")

    def on_closing(self):
        """Método chamado quando a janela é fechada"""
        try:
            # Parar monitoramento de logs
            self.stop_log_monitoring()

            # Parar servidor se estiver rodando
            if self.server_running:
                self.stop_server()

            # Fechar janela
            self.root.destroy()

        except Exception as e:
            print(f"Erro ao fechar aplicação: {e}")
            self.root.destroy()

    def test_monitoring(self):
        """Testa o sistema de monitoramento em tempo real"""
        try:
            self.show_message("🔧 Testando sistema de monitoramento...")

            # Verificar se o observer está rodando
            if self.log_observer and self.log_observer.is_alive():
                self.show_message("✅ Observer está ativo")
            else:
                self.show_message("❌ Observer não está ativo")
                self.show_message("🔄 Tentando reiniciar monitoramento...")
                self.stop_log_monitoring()
                self.start_log_monitoring()
                return

            # Verificar pastas de logs
            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                if os.path.exists(path):
                    log_path_found = path
                    self.show_message(f"📁 Pasta de logs encontrada: {path}")
                    break

            if not log_path_found:
                self.show_message("❌ Nenhuma pasta de logs encontrada!")
                return

            # Listar arquivos de log
            log_files = [f for f in os.listdir(log_path_found) if f.endswith('.log')]
            self.show_message(f"📋 Arquivos de log: {len(log_files)}")

            if not log_files:
                self.show_message("❌ Nenhum arquivo .log encontrado!")
                return

            # Verificar arquivo mais recente
            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_path_found, f)))
            log_path = os.path.join(log_path_found, latest_log)

            size = os.path.getsize(log_path)
            mtime = os.path.getmtime(log_path)
            import datetime
            mod_time = datetime.datetime.fromtimestamp(mtime).strftime("%H:%M:%S")

            self.show_message(f"📄 Log mais recente: {latest_log}")
            self.show_message(f"📊 Tamanho: {size} bytes, Modificado: {mod_time}")

            # Verificar posições do handler
            if hasattr(self, 'log_handler') and hasattr(self.log_handler, 'last_positions'):
                if log_path in self.log_handler.last_positions:
                    pos = self.log_handler.last_positions[log_path]
                    self.show_message(f"📍 Posição atual no arquivo: {pos}/{size}")
                else:
                    self.show_message("📍 Arquivo ainda não foi processado")

            # Verificar jogadores conhecidos
            online_count = len([p for p in self.known_players.values() if p['status'] == 'online'])
            total_count = len(self.known_players)

            self.show_message(f"👥 Jogadores conhecidos: {total_count}")
            self.show_message(f"🟢 Jogadores online: {online_count}")

            if self.known_players:
                self.show_message("📝 Lista de jogadores:")
                for steam_id, player in self.known_players.items():
                    status_icon = "🟢" if player['status'] == 'online' else "🔴"
                    self.show_message(f"   {status_icon} {player['name']} ({steam_id[-4:]})")

            # Simular evento para testar
            self.show_message("🧪 Simulando evento de teste...")
            if hasattr(self, 'log_handler'):
                # Forçar processamento do arquivo atual
                self.log_handler.process_log_file(log_path)
                self.show_message("✅ Processamento forçado concluído")

        except Exception as e:
            self.show_message(f"❌ Erro no teste: {e}")

    def backup_player_scan(self):
        """Sistema de backup para detectar jogadores se o watchdog falhar"""
        try:
            # Só executar se não temos jogadores detectados pelo watchdog
            online_count = len([p for p in self.known_players.values() if p['status'] == 'online'])
            if online_count > 0:
                return  # Watchdog está funcionando

            # Encontrar pasta de logs
            possible_paths = [self.logs_path, "../logs", "logs", "../VRisingServer_Data/StreamingAssets/Logs"]
            log_path_found = None

            for path in possible_paths:
                if os.path.exists(path):
                    log_path_found = path
                    break

            if not log_path_found:
                return

            # Encontrar arquivo mais recente
            log_files = [f for f in os.listdir(log_path_found) if f.endswith('.log')]
            if not log_files:
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(log_path_found, f)))
            log_path = os.path.join(log_path_found, latest_log)

            # Verificar se arquivo foi modificado recentemente (últimos 5 minutos)
            import time
            current_time = time.time()
            file_mtime = os.path.getmtime(log_path)

            if current_time - file_mtime > 300:  # 5 minutos
                return  # Arquivo muito antigo, servidor provavelmente offline

            # Ler últimas linhas do arquivo
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            # Analisar últimas 50 linhas em busca de jogadores conectados
            recent_lines = lines[-50:] if len(lines) > 50 else lines

            import re
            current_players = {}

            for line in recent_lines:
                line = line.strip()
                if not line:
                    continue

                # Padrões de conexão
                connection_patterns = [
                    r"User\s*'\{[^}]+\}'\s*'(\d{17})',.*Character:\s*'([^']+)'\s*connected",
                    r"'(\d{17})'.*Character:\s*'([^']+)'.*connected",
                    r"(\d{17}).*Character:\s*'([^']+)'.*connected"
                ]

                for pattern in connection_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        steam_id = match.group(1)
                        character_name = match.group(2) if len(match.groups()) > 1 else f"Jogador_{steam_id[-4:]}"

                        # Extrair timestamp
                        time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                        connect_time = time_match.group(1) if time_match else "Detectado"

                        current_players[steam_id] = {
                            'name': character_name,
                            'steam_id': steam_id,
                            'connect_time': connect_time,
                            'status': 'online'
                        }
                        break

                # Verificar desconexões
                disconnect_patterns = [
                    r"User.*'(\d{17})'.*disconnected",
                    r"'(\d{17})'.*disconnected",
                    r"(\d{17}).*disconnected"
                ]

                for pattern in disconnect_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        steam_id = match.group(1)
                        if steam_id in current_players:
                            current_players[steam_id]['status'] = 'offline'
                        break

            # Atualizar jogadores conhecidos apenas com os que estão online
            new_online_players = {k: v for k, v in current_players.items() if v['status'] == 'online'}

            if new_online_players and len(new_online_players) != len([p for p in self.known_players.values() if p['status'] == 'online']):
                print(f"Debug: 🔄 Backup scan detectou {len(new_online_players)} jogador(es) online")

                # Atualizar lista
                for steam_id, player in new_online_players.items():
                    if steam_id not in self.known_players:
                        self.known_players[steam_id] = player
                        print(f"Debug: ✅ Backup detectou: {player['name']} ({steam_id[-4:]})")

                # Marcar jogadores que não estão mais online
                for steam_id, player in self.known_players.items():
                    if steam_id not in new_online_players and player['status'] == 'online':
                        self.known_players[steam_id]['status'] = 'offline'
                        print(f"Debug: ❌ Backup detectou saída: {player['name']} ({steam_id[-4:]})")

        except Exception as e:
            print(f"Erro no backup scan: {e}")

    def show_commands(self):
        """Mostra janela com comandos úteis do servidor"""
        import tkinter.messagebox as msgbox

        commands_text = """
🎮 COMANDOS ÚTEIS DO V RISING SERVER

📋 Como usar Steam IDs:
• Steam ID tem 17 dígitos
• Exemplo: 76561198000000000
• Para encontrar: steamid.io ou perfil Steam

👑 Comandos de Admin (no chat do jogo):
• !admin - Verificar se é admin
• !kick [nome] - Expulsar jogador
• !ban [nome] - Banir jogador
• !unban [steamid] - Desbanir jogador

🔧 Arquivos importantes:
• adminlist.txt - Lista de administradores
• banlist.txt - Lista de jogadores banidos
• ServerGameSettings.json - Configurações do jogo

💡 Dicas:
• Reinicie o servidor após mudanças nos arquivos
• Faça backup antes de modificar configurações
• Use este gerenciador para facilitar o processo

🌐 Para acesso externo:
• Configure port forwarding no roteador
• Libere a porta no Windows Firewall
• Use IP externo + porta configurada
        """

        msgbox.showinfo("Comandos Úteis", commands_text)

    def run(self):
        """Inicia a aplicação"""
        try:
            self.root.mainloop()
        finally:
            self.monitoring_active = False
            if self.server_running and self.server_process:
                self.server_process.terminate()

if __name__ == "__main__":
    app = VRisingManager()
    app.run()
