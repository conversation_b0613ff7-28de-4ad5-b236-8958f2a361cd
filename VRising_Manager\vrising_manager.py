#!/usr/bin/env python3
"""
V Rising Server Manager
Interface moderna para gerenciar servidor V Rising
"""

import tkinter as tk
import sys
import os

# Verificar dependências antes de importar
try:
    import customtkinter as ctk
    import psutil
except ImportError as e:
    print(f"❌ Erro: Dependência não encontrada: {e}")
    print("\n📦 Para instalar as dependências:")
    print("pip install customtkinter psutil")
    print("\nOu execute install.bat")
    input("\nPressione Enter para sair...")
    sys.exit(1)

import subprocess
import threading
import json
import datetime
import zipfile
from pathlib import Path
import time

# Configuração do tema
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class VRisingManager:
    def __init__(self):
        # Verificar se o executável do servidor existe (na pasta pai)
        self.server_exe = "../VRisingServer.exe"
        if not os.path.exists(self.server_exe):
            # Tentar na pasta atual também
            self.server_exe = "VRisingServer.exe"
            if not os.path.exists(self.server_exe):
                self.show_error_and_exit(
                    "❌ VRisingServer.exe não encontrado!\n\n"
                    "Certifique-se de que a pasta VRising_Manager está\n"
                    "no diretório do servidor V Rising dedicado.\n\n"
                    "Estrutura esperada:\n"
                    "VRisingServer/\n"
                    "├── VRisingServer.exe\n"
                    "└── VRising_Manager/\n"
                    "    └── vrising_manager.py"
                )

        self.root = ctk.CTk()
        self.root.title("V Rising Server Manager")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Variáveis de controle
        self.server_process = None
        self.server_running = False
        self.monitoring_active = False

        # Caminhos (relativos à pasta do servidor)
        # Verificar se estamos na pasta do manager ou na pasta do servidor
        if os.path.exists("../save-data"):
            # Executando da pasta VRising_Manager
            self.data_path = "../save-data"
            self.logs_path = "../logs"
            self.backups_path = "../backups"
        else:
            # Executando da pasta do servidor
            self.data_path = "save-data"
            self.logs_path = "logs"
            self.backups_path = "backups"
        
        # Configurações padrão
        self.config = {
            "server_name": "VRising Server",
            "save_name": "crepusculo",
            "port": 9876,
            "max_players": 40,
            "auto_backup": True,
            "backup_interval": 30  # minutos
        }
        
        self.load_config()
        self.setup_ui()
        self.start_monitoring()

    def show_error_and_exit(self, message):
        """Mostra erro crítico e sai da aplicação"""
        import tkinter.messagebox as msgbox
        root = tk.Tk()
        root.withdraw()  # Esconder janela principal
        msgbox.showerror("Erro Crítico", message)
        root.destroy()
        sys.exit(1)
        
    def setup_ui(self):
        """Configura a interface principal"""
        # Frame principal
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Título
        title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧛 V Rising Server Manager", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(10, 20))
        
        # Status do servidor
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame, 
            text="🔴 Servidor Offline", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # Botões de controle
        self.control_frame = ctk.CTkFrame(self.status_frame)
        self.control_frame.pack(side="right", padx=20, pady=10)
        
        self.start_btn = ctk.CTkButton(
            self.control_frame, 
            text="▶️ Iniciar", 
            command=self.start_server,
            width=100
        )
        self.start_btn.pack(side="left", padx=5)
        
        self.stop_btn = ctk.CTkButton(
            self.control_frame, 
            text="⏹️ Parar", 
            command=self.stop_server,
            width=100,
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=5)
        
        self.restart_btn = ctk.CTkButton(
            self.control_frame, 
            text="🔄 Reiniciar", 
            command=self.restart_server,
            width=100,
            state="disabled"
        )
        self.restart_btn.pack(side="left", padx=5)
        
        # Notebook para abas
        self.notebook = ctk.CTkTabview(self.main_frame)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Criar abas
        self.create_dashboard_tab()
        self.create_config_tab()
        self.create_backup_tab()
        self.create_players_tab()
        self.create_logs_tab()
        
    def create_dashboard_tab(self):
        """Cria a aba do dashboard"""
        tab = self.notebook.add("📊 Dashboard")
        
        # Frame de estatísticas
        stats_frame = ctk.CTkFrame(tab)
        stats_frame.pack(fill="x", padx=10, pady=10)
        
        stats_title = ctk.CTkLabel(stats_frame, text="Estatísticas do Servidor", font=ctk.CTkFont(size=18, weight="bold"))
        stats_title.pack(pady=(10, 5))
        
        # Grid de estatísticas
        self.stats_grid = ctk.CTkFrame(stats_frame)
        self.stats_grid.pack(fill="x", padx=10, pady=10)
        
        # Labels de estatísticas
        self.uptime_label = ctk.CTkLabel(self.stats_grid, text="⏱️ Uptime: --")
        self.uptime_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        self.players_label = ctk.CTkLabel(self.stats_grid, text="👥 Jogadores: 0/40")
        self.players_label.grid(row=0, column=1, padx=10, pady=5, sticky="w")
        
        self.cpu_label = ctk.CTkLabel(self.stats_grid, text="💻 CPU: --%")
        self.cpu_label.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        self.memory_label = ctk.CTkLabel(self.stats_grid, text="🧠 RAM: -- MB")
        self.memory_label.grid(row=1, column=1, padx=10, pady=5, sticky="w")
        
        # Console de logs em tempo real
        console_frame = ctk.CTkFrame(tab)
        console_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        console_title = ctk.CTkLabel(console_frame, text="Console do Servidor", font=ctk.CTkFont(size=18, weight="bold"))
        console_title.pack(pady=(10, 5))
        
        self.console_text = ctk.CTkTextbox(console_frame, height=300)
        self.console_text.pack(fill="both", expand=True, padx=10, pady=10)
        
    def create_config_tab(self):
        """Cria a aba de configurações"""
        tab = self.notebook.add("⚙️ Configurações")
        
        # Frame scrollável
        scroll_frame = ctk.CTkScrollableFrame(tab)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Configurações do servidor
        server_frame = ctk.CTkFrame(scroll_frame)
        server_frame.pack(fill="x", pady=10)
        
        ctk.CTkLabel(server_frame, text="Configurações do Servidor", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))
        
        # Nome do servidor
        ctk.CTkLabel(server_frame, text="Nome do Servidor:").pack(anchor="w", padx=10)
        self.server_name_entry = ctk.CTkEntry(server_frame, width=300)
        self.server_name_entry.pack(padx=10, pady=(0, 10))
        self.server_name_entry.insert(0, self.config["server_name"])
        
        # Nome do save
        ctk.CTkLabel(server_frame, text="Nome do Save:").pack(anchor="w", padx=10)
        self.save_name_entry = ctk.CTkEntry(server_frame, width=300)
        self.save_name_entry.pack(padx=10, pady=(0, 10))
        self.save_name_entry.insert(0, self.config["save_name"])
        
        # Porta
        ctk.CTkLabel(server_frame, text="Porta:").pack(anchor="w", padx=10)
        self.port_entry = ctk.CTkEntry(server_frame, width=300)
        self.port_entry.pack(padx=10, pady=(0, 10))
        self.port_entry.insert(0, str(self.config["port"]))
        
        # Máximo de jogadores
        ctk.CTkLabel(server_frame, text="Máximo de Jogadores:").pack(anchor="w", padx=10)
        self.max_players_entry = ctk.CTkEntry(server_frame, width=300)
        self.max_players_entry.pack(padx=10, pady=(0, 10))
        self.max_players_entry.insert(0, str(self.config["max_players"]))
        
        # Botão salvar
        save_btn = ctk.CTkButton(server_frame, text="💾 Salvar Configurações", command=self.save_config)
        save_btn.pack(pady=10)
        
    def create_backup_tab(self):
        """Cria a aba de backups"""
        tab = self.notebook.add("💾 Backups")
        
        # Controles de backup
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(control_frame, text="Gerenciamento de Backups", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))
        
        btn_frame = ctk.CTkFrame(control_frame)
        btn_frame.pack(pady=10)
        
        backup_btn = ctk.CTkButton(btn_frame, text="📦 Criar Backup", command=self.create_backup)
        backup_btn.pack(side="left", padx=5)
        
        restore_btn = ctk.CTkButton(btn_frame, text="📥 Restaurar Backup", command=self.restore_backup)
        restore_btn.pack(side="left", padx=5)
        
        # Lista de backups
        list_frame = ctk.CTkFrame(tab)
        list_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        ctk.CTkLabel(list_frame, text="Backups Disponíveis", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))
        
        self.backup_listbox = ctk.CTkTextbox(list_frame, height=300)
        self.backup_listbox.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.refresh_backup_list()
        
    def create_players_tab(self):
        """Cria a aba de jogadores"""
        tab = self.notebook.add("👥 Jogadores")

        # Frame de controles
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(control_frame, text="Gerenciamento de Jogadores", font=ctk.CTkFont(size=18, weight="bold")).pack(pady=(10, 5))

        # Botões de atualização
        btn_frame = ctk.CTkFrame(control_frame)
        btn_frame.pack(pady=10)

        refresh_players_btn = ctk.CTkButton(btn_frame, text="🔄 Atualizar Lista", command=self.refresh_players)
        refresh_players_btn.pack(side="left", padx=5)

        debug_btn = ctk.CTkButton(btn_frame, text="🔍 Debug Logs", command=self.debug_player_logs)
        debug_btn.pack(side="left", padx=5)

        # Frame principal dividido em duas colunas
        main_frame = ctk.CTkFrame(tab)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna esquerda - Jogadores Online
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)

        ctk.CTkLabel(left_frame, text="👥 Jogadores Online", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        self.players_listbox = ctk.CTkTextbox(left_frame, height=200)
        self.players_listbox.pack(fill="both", expand=True, padx=10, pady=10)

        # Coluna direita - Ações
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", padx=(5, 10), pady=10, expand=False)
        right_frame.configure(width=250)  # Largura fixa maior

        ctk.CTkLabel(right_frame, text="⚙️ Ações", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 5))

        # Campo para inserir Steam ID ou nome
        ctk.CTkLabel(right_frame, text="Steam ID ou Nome:").pack(anchor="w", padx=10, pady=(10, 0))
        self.player_id_entry = ctk.CTkEntry(right_frame, width=200, placeholder_text="Ex: 76561198000000000")
        self.player_id_entry.pack(padx=10, pady=5)

        # Botões de ação
        admin_btn = ctk.CTkButton(right_frame, text="👑 Tornar Admin", command=self.make_admin, width=180)
        admin_btn.pack(padx=10, pady=5)

        remove_admin_btn = ctk.CTkButton(right_frame, text="👤 Remover Admin", command=self.remove_admin, width=180)
        remove_admin_btn.pack(padx=10, pady=5)

        ban_btn = ctk.CTkButton(right_frame, text="🚫 Banir Jogador", command=self.ban_player, width=180, fg_color="red")
        ban_btn.pack(padx=10, pady=5)

        unban_btn = ctk.CTkButton(right_frame, text="✅ Desbanir Jogador", command=self.unban_player, width=180, fg_color="green")
        unban_btn.pack(padx=10, pady=5)

        # Separador
        separator = ctk.CTkFrame(right_frame, height=2)
        separator.pack(fill="x", padx=10, pady=10)

        # Listas de admins e banidos
        ctk.CTkLabel(right_frame, text="👑 Administradores", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(10, 5))
        self.admins_listbox = ctk.CTkTextbox(right_frame, height=180, width=200)
        self.admins_listbox.pack(padx=10, pady=5, fill="both", expand=True)

        ctk.CTkLabel(right_frame, text="🚫 Jogadores Banidos", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(10, 5))
        self.banned_listbox = ctk.CTkTextbox(right_frame, height=180, width=200)
        self.banned_listbox.pack(padx=10, pady=5, fill="both", expand=True)

        # Botão para comandos úteis
        commands_btn = ctk.CTkButton(right_frame, text="📋 Comandos Úteis", command=self.show_commands, width=180)
        commands_btn.pack(padx=10, pady=10)

        # Inicializar listas
        self.refresh_players()
        self.refresh_admin_lists()

        # Configurar callback para quando a aba for selecionada
        def on_tab_change():
            if self.notebook.get() == "👥 Jogadores":
                self.root.after(100, self.refresh_admin_lists)  # Atualizar após 100ms

        self.notebook.configure(command=on_tab_change)
        
    def create_logs_tab(self):
        """Cria a aba de logs"""
        tab = self.notebook.add("📋 Logs")
        
        # Controles
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)
        
        refresh_btn = ctk.CTkButton(control_frame, text="🔄 Atualizar", command=self.refresh_logs)
        refresh_btn.pack(side="left", padx=10, pady=10)
        
        clear_btn = ctk.CTkButton(control_frame, text="🗑️ Limpar", command=self.clear_logs)
        clear_btn.pack(side="left", padx=10, pady=10)
        
        # Área de logs
        self.logs_text = ctk.CTkTextbox(tab, height=400)
        self.logs_text.pack(fill="both", expand=True, padx=10, pady=10)
        
    def load_config(self):
        """Carrega configurações do arquivo"""
        config_file = "vrising_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config.update(json.load(f))
            except Exception as e:
                print(f"Erro ao carregar configurações: {e}")
                
    def save_config(self):
        """Salva configurações no arquivo"""
        try:
            self.config["server_name"] = self.server_name_entry.get()
            self.config["save_name"] = self.save_name_entry.get()
            self.config["port"] = int(self.port_entry.get())
            self.config["max_players"] = int(self.max_players_entry.get())
            
            with open("vrising_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            # Mostrar confirmação
            self.show_message("✅ Configurações salvas com sucesso!")
            
        except Exception as e:
            self.show_message(f"❌ Erro ao salvar configurações: {e}")
            
    def show_message(self, message):
        """Mostra mensagem no console"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.console_text.insert("end", f"[{timestamp}] {message}\n")
        self.console_text.see("end")

    def start_server(self):
        """Inicia o servidor V Rising"""
        if self.server_running:
            self.show_message("⚠️ Servidor já está rodando!")
            return

        try:
            # Criar diretórios necessários
            os.makedirs(self.logs_path, exist_ok=True)
            os.makedirs(self.backups_path, exist_ok=True)

            # Criar backup automático antes de iniciar
            if self.config.get("auto_backup", True):
                self.create_backup(auto=True)

            # Comando para iniciar o servidor
            # Usar caminho absoluto para data path
            data_path_abs = os.path.abspath(self.data_path)

            cmd = [
                self.server_exe,
                "-persistentDataPath", data_path_abs,
                "-serverName", self.config["server_name"],
                "-saveName", self.config["save_name"],
                "-port", str(self.config["port"])
            ]

            self.show_message(f"🚀 Iniciando servidor: {' '.join(cmd)}")

            # Iniciar processo em thread separada
            def run_server():
                try:
                    self.server_process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        universal_newlines=True,
                        bufsize=1
                    )

                    self.server_running = True
                    self.root.after(0, self.update_server_status)

                    # Ler output do servidor
                    for line in iter(self.server_process.stdout.readline, ''):
                        if line:
                            self.root.after(0, lambda l=line: self.show_message(l.strip()))

                    self.server_process.wait()

                except Exception as e:
                    self.root.after(0, lambda: self.show_message(f"❌ Erro ao iniciar servidor: {e}"))
                finally:
                    self.server_running = False
                    self.server_process = None
                    self.root.after(0, self.update_server_status)

            threading.Thread(target=run_server, daemon=True).start()

        except Exception as e:
            self.show_message(f"❌ Erro ao iniciar servidor: {e}")

    def stop_server(self):
        """Para o servidor V Rising"""
        if not self.server_running or not self.server_process:
            self.show_message("⚠️ Servidor não está rodando!")
            return

        try:
            self.show_message("🛑 Parando servidor...")
            self.server_process.terminate()

            # Aguardar um pouco e forçar se necessário
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.show_message("⚠️ Forçando parada do servidor...")
                self.server_process.kill()

            self.server_running = False
            self.server_process = None
            self.update_server_status()
            self.show_message("✅ Servidor parado com sucesso!")

        except Exception as e:
            self.show_message(f"❌ Erro ao parar servidor: {e}")

    def restart_server(self):
        """Reinicia o servidor V Rising"""
        self.show_message("🔄 Reiniciando servidor...")
        self.stop_server()

        # Aguardar um pouco antes de reiniciar
        def delayed_start():
            time.sleep(3)
            self.root.after(0, self.start_server)

        threading.Thread(target=delayed_start, daemon=True).start()

    def update_server_status(self):
        """Atualiza o status visual do servidor"""
        if self.server_running:
            self.status_label.configure(text="🟢 Servidor Online")
            self.start_btn.configure(state="disabled")
            self.stop_btn.configure(state="normal")
            self.restart_btn.configure(state="normal")
        else:
            self.status_label.configure(text="🔴 Servidor Offline")
            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.restart_btn.configure(state="disabled")

    def create_backup(self, auto=False):
        """Cria backup dos dados do servidor"""
        try:
            if not os.path.exists(self.data_path):
                self.show_message("⚠️ Diretório de dados não encontrado!")
                return

            timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"backup_{self.config['save_name']}_{timestamp}.zip"
            backup_path = os.path.join(self.backups_path, backup_name)

            prefix = "🤖 [AUTO]" if auto else "📦"
            self.show_message(f"{prefix} Criando backup: {backup_name}")

            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                saves_path = os.path.join(self.data_path, "Saves")
                if os.path.exists(saves_path):
                    for root, dirs, files in os.walk(saves_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, self.data_path)
                            zipf.write(file_path, arcname)

            self.show_message(f"✅ Backup criado: {backup_name}")
            self.refresh_backup_list()

        except Exception as e:
            self.show_message(f"❌ Erro ao criar backup: {e}")

    def restore_backup(self):
        """Restaura backup selecionado"""
        # Implementação simplificada - em produção seria mais robusta
        self.show_message("🚧 Funcionalidade de restauração em desenvolvimento")

    def refresh_backup_list(self):
        """Atualiza lista de backups"""
        try:
            self.backup_listbox.delete("1.0", "end")

            if not os.path.exists(self.backups_path):
                self.backup_listbox.insert("end", "Nenhum backup encontrado.\n")
                return

            backups = []
            for file in os.listdir(self.backups_path):
                if file.endswith('.zip'):
                    file_path = os.path.join(self.backups_path, file)
                    size = os.path.getsize(file_path)
                    size_mb = size / (1024 * 1024)
                    mtime = os.path.getmtime(file_path)
                    date_str = datetime.datetime.fromtimestamp(mtime).strftime("%d/%m/%Y %H:%M")
                    backups.append((file, date_str, size_mb))

            backups.sort(key=lambda x: x[1], reverse=True)

            for backup, date, size in backups:
                self.backup_listbox.insert("end", f"📦 {backup}\n")
                self.backup_listbox.insert("end", f"   📅 {date} | 💾 {size:.1f} MB\n\n")

        except Exception as e:
            self.backup_listbox.insert("end", f"❌ Erro ao listar backups: {e}\n")

    def refresh_logs(self):
        """Atualiza visualização de logs"""
        try:
            self.logs_text.delete("1.0", "end")

            if not os.path.exists(self.logs_path):
                self.logs_text.insert("end", "Nenhum log encontrado.\n")
                return

            # Pegar o log mais recente
            log_files = [f for f in os.listdir(self.logs_path) if f.endswith('.log')]
            if not log_files:
                self.logs_text.insert("end", "Nenhum arquivo de log encontrado.\n")
                return

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(self.logs_path, f)))
            log_path = os.path.join(self.logs_path, latest_log)

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                self.logs_text.insert("end", content)

            self.logs_text.see("end")

        except Exception as e:
            self.logs_text.insert("end", f"❌ Erro ao carregar logs: {e}\n")

    def clear_logs(self):
        """Limpa visualização de logs"""
        self.logs_text.delete("1.0", "end")

    def start_monitoring(self):
        """Inicia monitoramento do sistema"""
        self.monitoring_active = True

        def monitor():
            player_update_counter = 0
            while self.monitoring_active:
                try:
                    if self.server_running and self.server_process:
                        # Atualizar estatísticas
                        process = psutil.Process(self.server_process.pid)
                        cpu_percent = process.cpu_percent()
                        memory_mb = process.memory_info().rss / (1024 * 1024)

                        # Calcular uptime
                        create_time = process.create_time()
                        uptime_seconds = time.time() - create_time
                        uptime_str = str(datetime.timedelta(seconds=int(uptime_seconds)))

                        # Atualizar UI
                        self.root.after(0, lambda: self.update_stats(uptime_str, cpu_percent, memory_mb))

                        # Atualizar lista de jogadores a cada 30 segundos
                        player_update_counter += 1
                        if player_update_counter >= 6:  # 6 * 5 segundos = 30 segundos
                            self.root.after(0, self.refresh_players)
                            player_update_counter = 0

                except (psutil.NoSuchProcess, psutil.AccessDenied, AttributeError):
                    pass
                except Exception as e:
                    print(f"Erro no monitoramento: {e}")

                time.sleep(5)  # Atualizar a cada 5 segundos

        threading.Thread(target=monitor, daemon=True).start()

    def update_stats(self, uptime, cpu, memory):
        """Atualiza estatísticas na UI"""
        try:
            self.uptime_label.configure(text=f"⏱️ Uptime: {uptime}")
            self.cpu_label.configure(text=f"💻 CPU: {cpu:.1f}%")
            self.memory_label.configure(text=f"🧠 RAM: {memory:.1f} MB")
        except:
            pass

    def refresh_players(self):
        """Atualiza lista de jogadores online"""
        try:
            self.players_listbox.delete("1.0", "end")

            if not self.server_running:
                self.players_listbox.insert("end", "⚠️ Servidor offline\n")
                try:
                    self.players_label.configure(text="👥 Jogadores: 0/40")
                except:
                    pass
                return

            # Tentar ler jogadores dos logs
            online_players = self.get_online_players_from_logs()

            if online_players:
                self.players_listbox.insert("end", f"👥 Jogadores Online ({len(online_players)}):\n")
                self.players_listbox.insert("end", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")

                for i, player in enumerate(online_players, 1):
                    name = player.get('name', 'Desconhecido')
                    steam_id = player.get('steam_id', 'N/A')
                    connect_time = player.get('connect_time', 'N/A')

                    self.players_listbox.insert("end", f"{i}. 🎮 {name}\n")
                    self.players_listbox.insert("end", f"   Steam ID: {steam_id}\n")
                    self.players_listbox.insert("end", f"   Conectado: {connect_time}\n\n")

                try:
                    self.players_label.configure(text=f"👥 Jogadores: {len(online_players)}/{self.config['max_players']}")
                except:
                    pass
            else:
                self.players_listbox.insert("end", "🔍 Monitoramento de jogadores\n")
                self.players_listbox.insert("end", "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")
                self.players_listbox.insert("end", "📊 Status: Nenhum jogador detectado\n\n")
                self.players_listbox.insert("end", "💡 Como funciona:\n")
                self.players_listbox.insert("end", "• Analisa logs do servidor em tempo real\n")
                self.players_listbox.insert("end", "• Detecta conexões e desconexões\n")
                self.players_listbox.insert("end", "• Mostra Steam IDs dos jogadores\n\n")
                self.players_listbox.insert("end", "🔧 Para gerenciar jogadores:\n")
                self.players_listbox.insert("end", "• Use Steam ID de 17 dígitos\n")
                self.players_listbox.insert("end", "• Exemplo: 76561198000000000\n")

                try:
                    self.players_label.configure(text="👥 Jogadores: 0/40")
                except:
                    pass

        except Exception as e:
            self.players_listbox.insert("end", f"❌ Erro ao atualizar jogadores: {e}\n")

    def get_online_players_from_logs(self):
        """Extrai jogadores online dos logs do servidor"""
        try:
            if not os.path.exists(self.logs_path):
                return []

            # Pegar o log mais recente
            log_files = [f for f in os.listdir(self.logs_path) if f.endswith('.log')]
            if not log_files:
                return []

            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(self.logs_path, f)))
            log_path = os.path.join(self.logs_path, latest_log)

            # Dicionário para rastrear jogadores
            players = {}
            import re

            # Ler as últimas linhas do log
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

                # Analisar últimas 2000 linhas para encontrar conexões/desconexões
                for line in lines[-2000:]:
                    line = line.strip()

                    # Padrões específicos do V Rising baseados no log real
                    connection_patterns = [
                        r"User.*'(\d{17})'.*Character:\s*'([^']+)'.*connected.*Entity\s*'(\d+,\d+)'",  # Padrão completo
                        r"User.*'(\d{17})'.*connected",  # Padrão simplificado
                        r"(\d{17}).*Character:\s*'([^']+)'.*connected",  # Alternativo
                        r".*'(\d{17})'.*connected"  # Genérico
                    ]

                    # Padrões para detectar desconexões do V Rising
                    disconnection_patterns = [
                        r"User.*'(\d{17})'.*disconnected",
                        r"(\d{17}).*disconnected",
                        r".*'(\d{17})'.*left",
                        r".*'(\d{17})'.*quit"
                    ]

                    # Verificar conexões
                    for pattern in connection_patterns:
                        match = re.search(pattern, line, re.IGNORECASE)
                        if match:
                            steam_id = match.group(1)

                            # Extrair nome do personagem do padrão específico do V Rising
                            character_name = "Jogador"

                            # Padrão: User '{Lidgren 192.168.1.100:53283}' '76561198832506980', approvedUserIndex: 0, Character: 'Renee' connected
                            char_match = re.search(r"Character:\s*'([^']+)'", line)
                            if char_match:
                                character_name = char_match.group(1)
                            else:
                                # Tentar outros padrões
                                name_patterns = [
                                    r"User\s*'[^']*'\s*'(\d{17})'.*Character:\s*'([^']+)'",
                                    r"'([^']+)'.*connected"
                                ]

                                for name_pattern in name_patterns:
                                    name_match = re.search(name_pattern, line)
                                    if name_match and len(name_match.groups()) >= 2:
                                        character_name = name_match.group(2)
                                        break

                            # Extrair timestamp do início da linha [HH:MM:SS]
                            time_match = re.search(r'\[(\d{1,2}:\d{2}:\d{2})\]', line)
                            connect_time = time_match.group(1) if time_match else "Agora"

                            players[steam_id] = {
                                'name': character_name,
                                'steam_id': steam_id,
                                'connect_time': connect_time,
                                'status': 'online',
                                'last_seen': line
                            }
                            break

                    # Verificar desconexões
                    for pattern in disconnection_patterns:
                        match = re.search(pattern, line, re.IGNORECASE)
                        if match:
                            steam_id = match.group(1)
                            if steam_id in players:
                                players[steam_id]['status'] = 'offline'
                            break

            # Retornar apenas jogadores online
            online_players = [player for player in players.values() if player['status'] == 'online']

            # Se não encontrou jogadores nos logs, tentar método alternativo
            if not online_players:
                online_players = self.get_players_alternative_method()

            return online_players

        except Exception as e:
            print(f"Erro ao analisar logs: {e}")
            return []

    def get_players_alternative_method(self):
        """Método alternativo para detectar jogadores (simulação para teste)"""
        try:
            # Verificar se há conexões de rede ativas na porta do servidor
            import psutil

            if not self.server_running or not self.server_process:
                return []

            # Obter conexões do processo do servidor
            try:
                process = psutil.Process(self.server_process.pid)
                connections = process.connections()

                # Contar conexões estabelecidas na porta do servidor
                established_connections = [
                    conn for conn in connections
                    if conn.status == 'ESTABLISHED' and
                    conn.laddr.port == self.config.get('port', 9876)
                ]

                # Simular jogadores baseado nas conexões
                players = []
                for i, conn in enumerate(established_connections):
                    players.append({
                        'name': f'Jogador_{i+1}',
                        'steam_id': f'7656119800000000{i}',
                        'connect_time': 'Ativo',
                        'status': 'online',
                        'ip': conn.raddr.ip if conn.raddr else 'Local'
                    })

                return players

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                return []

        except Exception as e:
            print(f"Erro no método alternativo: {e}")
            return []

    def refresh_admin_lists(self):
        """Atualiza listas de admins e banidos"""
        try:
            # Atualizar lista de admins
            self.admins_listbox.delete("1.0", "end")
            admin_file = os.path.join(self.data_path, "Settings", "adminlist.txt")

            self.show_message(f"🔍 Verificando arquivo de admins: {admin_file}")

            if os.path.exists(admin_file):
                with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    admins = [line.strip() for line in lines if line.strip()]

                    if admins:
                        self.admins_listbox.insert("end", f"👑 {len(admins)} Admin(s):\n\n")
                        for i, admin in enumerate(admins, 1):
                            # Mostrar apenas os últimos 4 dígitos para privacidade
                            admin_display = f"***{admin[-4:]}" if len(admin) > 4 else admin
                            self.admins_listbox.insert("end", f"{i}. {admin_display}\n")
                            self.admins_listbox.insert("end", f"   ID: {admin}\n\n")
                        self.show_message(f"✅ {len(admins)} administrador(es) carregado(s)")
                    else:
                        self.admins_listbox.insert("end", "❌ Nenhum admin\nconfigurado\n\n")
                        self.admins_listbox.insert("end", "💡 Para adicionar:\n")
                        self.admins_listbox.insert("end", "1. Digite Steam ID\n")
                        self.admins_listbox.insert("end", "2. Clique 'Tornar Admin'")
                        self.show_message("⚠️ Arquivo de admins está vazio")
            else:
                self.admins_listbox.insert("end", "Arquivo adminlist.txt\nnão encontrado")
                self.show_message("⚠️ Arquivo adminlist.txt não encontrado")

            # Atualizar lista de banidos
            self.banned_listbox.delete("1.0", "end")
            ban_file = os.path.join(self.data_path, "Settings", "banlist.txt")

            if os.path.exists(ban_file):
                with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    banned = [line.strip() for line in lines if line.strip()]

                    if banned:
                        self.banned_listbox.insert("end", f"🚫 {len(banned)} Banido(s):\n\n")
                        for i, ban in enumerate(banned, 1):
                            # Mostrar apenas os últimos 4 dígitos para privacidade
                            ban_display = f"***{ban[-4:]}" if len(ban) > 4 else ban
                            self.banned_listbox.insert("end", f"{i}. {ban_display}\n")
                            self.banned_listbox.insert("end", f"   ID: {ban}\n\n")
                    else:
                        self.banned_listbox.insert("end", "✅ Nenhum jogador\nbanido\n\n")
                        self.banned_listbox.insert("end", "💡 Para banir:\n")
                        self.banned_listbox.insert("end", "1. Digite Steam ID\n")
                        self.banned_listbox.insert("end", "2. Clique 'Banir Jogador'")
            else:
                self.banned_listbox.insert("end", "Arquivo banlist.txt\nnão encontrado")

        except Exception as e:
            self.show_message(f"❌ Erro ao atualizar listas: {e}")
            self.admins_listbox.insert("end", f"Erro: {e}")
            self.banned_listbox.insert("end", f"Erro: {e}")

    def make_admin(self):
        """Torna um jogador administrador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            # Criar diretório Settings se não existir
            settings_dir = os.path.join(self.data_path, "Settings")
            os.makedirs(settings_dir, exist_ok=True)

            admin_file = os.path.join(settings_dir, "adminlist.txt")

            # Ler admins existentes
            existing_admins = []
            if os.path.exists(admin_file):
                with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_admins = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se já é admin
            if player_id in existing_admins:
                self.show_message(f"⚠️ {player_id} já é administrador")
                return

            # Adicionar novo admin
            existing_admins.append(player_id)

            # Salvar lista atualizada
            with open(admin_file, 'w', encoding='utf-8') as f:
                for admin in existing_admins:
                    f.write(f"{admin}\n")

            self.show_message(f"👑 {player_id} foi promovido a administrador")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao tornar admin: {e}")

    def remove_admin(self):
        """Remove privilégios de administrador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            admin_file = os.path.join(self.data_path, "Settings", "adminlist.txt")

            if not os.path.exists(admin_file):
                self.show_message("⚠️ Arquivo adminlist.txt não encontrado")
                return

            # Ler admins existentes
            with open(admin_file, 'r', encoding='utf-8', errors='ignore') as f:
                existing_admins = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se é admin
            if player_id not in existing_admins:
                self.show_message(f"⚠️ {player_id} não é administrador")
                return

            # Remover admin
            existing_admins.remove(player_id)

            # Salvar lista atualizada
            with open(admin_file, 'w', encoding='utf-8') as f:
                for admin in existing_admins:
                    f.write(f"{admin}\n")

            self.show_message(f"👤 Privilégios de admin removidos de {player_id}")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao remover admin: {e}")

    def ban_player(self):
        """Bane um jogador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            # Criar diretório Settings se não existir
            settings_dir = os.path.join(self.data_path, "Settings")
            os.makedirs(settings_dir, exist_ok=True)

            ban_file = os.path.join(settings_dir, "banlist.txt")

            # Ler banidos existentes
            existing_banned = []
            if os.path.exists(ban_file):
                with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_banned = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se já está banido
            if player_id in existing_banned:
                self.show_message(f"⚠️ {player_id} já está banido")
                return

            # Adicionar à lista de banidos
            existing_banned.append(player_id)

            # Salvar lista atualizada
            with open(ban_file, 'w', encoding='utf-8') as f:
                for banned in existing_banned:
                    f.write(f"{banned}\n")

            self.show_message(f"🚫 {player_id} foi banido do servidor")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao banir jogador: {e}")

    def unban_player(self):
        """Remove ban de um jogador"""
        player_id = self.player_id_entry.get().strip()
        if not player_id:
            self.show_message("⚠️ Digite um Steam ID ou nome de jogador")
            return

        try:
            ban_file = os.path.join(self.data_path, "Settings", "banlist.txt")

            if not os.path.exists(ban_file):
                self.show_message("⚠️ Arquivo banlist.txt não encontrado")
                return

            # Ler banidos existentes
            with open(ban_file, 'r', encoding='utf-8', errors='ignore') as f:
                existing_banned = [line.strip() for line in f.readlines() if line.strip()]

            # Verificar se está banido
            if player_id not in existing_banned:
                self.show_message(f"⚠️ {player_id} não está banido")
                return

            # Remover da lista de banidos
            existing_banned.remove(player_id)

            # Salvar lista atualizada
            with open(ban_file, 'w', encoding='utf-8') as f:
                for banned in existing_banned:
                    f.write(f"{banned}\n")

            self.show_message(f"✅ Ban removido de {player_id}")
            self.refresh_admin_lists()
            self.player_id_entry.delete(0, "end")

        except Exception as e:
            self.show_message(f"❌ Erro ao desbanir jogador: {e}")

    def debug_player_logs(self):
        """Mostra informações de debug dos logs para detectar jogadores"""
        try:
            debug_window = ctk.CTkToplevel(self.root)
            debug_window.title("Debug - Logs de Jogadores")
            debug_window.geometry("800x600")

            # Área de texto para mostrar logs
            debug_text = ctk.CTkTextbox(debug_window, height=500)
            debug_text.pack(fill="both", expand=True, padx=10, pady=10)

            # Informações de debug
            debug_text.insert("end", "🔍 DEBUG - ANÁLISE DE LOGS DE JOGADORES\n")
            debug_text.insert("end", "=" * 50 + "\n\n")

            # Verificar se pasta de logs existe
            debug_text.insert("end", f"📁 Pasta de logs: {self.logs_path}\n")
            debug_text.insert("end", f"📁 Existe: {os.path.exists(self.logs_path)}\n\n")

            if not os.path.exists(self.logs_path):
                debug_text.insert("end", "❌ PROBLEMA: Pasta de logs não encontrada!\n")
                debug_text.insert("end", "💡 Solução: Inicie o servidor para gerar logs\n")
                return

            # Listar arquivos de log
            log_files = [f for f in os.listdir(self.logs_path) if f.endswith('.log')]
            debug_text.insert("end", f"📋 Arquivos de log encontrados: {len(log_files)}\n")

            for log_file in log_files[-5:]:  # Últimos 5 arquivos
                log_path = os.path.join(self.logs_path, log_file)
                size = os.path.getsize(log_path)
                debug_text.insert("end", f"  • {log_file} ({size} bytes)\n")

            if not log_files:
                debug_text.insert("end", "❌ PROBLEMA: Nenhum arquivo de log encontrado!\n")
                debug_text.insert("end", "💡 Solução: Inicie o servidor para gerar logs\n")
                return

            # Analisar log mais recente
            latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(self.logs_path, f)))
            log_path = os.path.join(self.logs_path, latest_log)

            debug_text.insert("end", f"\n📄 Analisando: {latest_log}\n")
            debug_text.insert("end", "-" * 30 + "\n")

            # Ler últimas 50 linhas do log
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()

            debug_text.insert("end", f"📊 Total de linhas: {len(lines)}\n")
            debug_text.insert("end", f"🔍 Analisando últimas 50 linhas:\n\n")

            # Mostrar últimas linhas relevantes
            import re
            relevant_lines = []

            for i, line in enumerate(lines[-50:], len(lines)-49):
                line = line.strip()

                # Procurar por padrões relacionados a jogadores
                keywords = ['player', 'user', 'connect', 'disconnect', 'join', 'left', 'login', 'logout']
                if any(keyword in line.lower() for keyword in keywords):
                    relevant_lines.append(f"Linha {i}: {line}")

                # Procurar Steam IDs
                steam_id_match = re.search(r'(\d{17})', line)
                if steam_id_match:
                    relevant_lines.append(f"Linha {i} [STEAM ID]: {line}")

            if relevant_lines:
                debug_text.insert("end", "🎯 Linhas relevantes encontradas:\n")
                for line in relevant_lines[-20:]:  # Últimas 20 linhas relevantes
                    debug_text.insert("end", f"{line}\n")
            else:
                debug_text.insert("end", "⚠️ Nenhuma linha relevante encontrada\n")
                debug_text.insert("end", "💡 Isso pode significar:\n")
                debug_text.insert("end", "  • Nenhum jogador conectou recentemente\n")
                debug_text.insert("end", "  • Formato de log diferente do esperado\n")
                debug_text.insert("end", "  • Servidor não está logando conexões\n")

            # Testar detecção atual
            debug_text.insert("end", f"\n🧪 TESTE DE DETECÇÃO ATUAL:\n")
            debug_text.insert("end", "-" * 30 + "\n")

            detected_players = self.get_online_players_from_logs()
            debug_text.insert("end", f"👥 Jogadores detectados: {len(detected_players)}\n")

            for player in detected_players:
                debug_text.insert("end", f"  • {player['name']} ({player['steam_id']})\n")

            if not detected_players:
                debug_text.insert("end", "❌ Nenhum jogador detectado pelo sistema atual\n")

        except Exception as e:
            debug_text.insert("end", f"❌ Erro no debug: {e}\n")

    def show_commands(self):
        """Mostra janela com comandos úteis do servidor"""
        import tkinter.messagebox as msgbox

        commands_text = """
🎮 COMANDOS ÚTEIS DO V RISING SERVER

📋 Como usar Steam IDs:
• Steam ID tem 17 dígitos
• Exemplo: 76561198000000000
• Para encontrar: steamid.io ou perfil Steam

👑 Comandos de Admin (no chat do jogo):
• !admin - Verificar se é admin
• !kick [nome] - Expulsar jogador
• !ban [nome] - Banir jogador
• !unban [steamid] - Desbanir jogador

🔧 Arquivos importantes:
• adminlist.txt - Lista de administradores
• banlist.txt - Lista de jogadores banidos
• ServerGameSettings.json - Configurações do jogo

💡 Dicas:
• Reinicie o servidor após mudanças nos arquivos
• Faça backup antes de modificar configurações
• Use este gerenciador para facilitar o processo

🌐 Para acesso externo:
• Configure port forwarding no roteador
• Libere a porta no Windows Firewall
• Use IP externo + porta configurada
        """

        msgbox.showinfo("Comandos Úteis", commands_text)

    def run(self):
        """Inicia a aplicação"""
        try:
            self.root.mainloop()
        finally:
            self.monitoring_active = False
            if self.server_running and self.server_process:
                self.server_process.terminate()

if __name__ == "__main__":
    app = VRisingManager()
    app.run()
