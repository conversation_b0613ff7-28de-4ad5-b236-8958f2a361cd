# 🔧 Solucionando Problemas - V Rising Manager

## ❌ Problema 1: "Não carregou o jogo que tinha salvo"

### 🔍 **Diagnóstico:**
O servidor não está carregando o save existente ("crepusculo").

### ✅ **Soluções:**

#### **1. Verificar Configuração do Save**
1. Abra o gerenciador
2. Vá para aba **"⚙️ Configurações"**
3. Verifique se **"Nome do Save"** está como: `crepusculo`
4. Clique em **"💾 Salvar Configurações"**

#### **2. Verificar Estrutura de Arquivos**
```
save-data/
└── Saves/
    └── v4/
        └── crepusculo/    ← Seu save deve estar aqui
```

#### **3. Comando de Inicialização Correto**
O servidor deve ser iniciado com:
```
VRisingServer.exe -persistentDataPath "save-data" -saveName "crepusculo"
```

---

## ❌ Problema 2: "Não consigo ver a lista de admins"

### 🔍 **Diagnóstico:**
O arquivo `adminlist.txt` existe mas não aparece na interface.

### ✅ **Soluções:**

#### **1. Verificar Arquivo de Admins**
1. Navegue até: `save-data/Settings/adminlist.txt`
2. Abra o arquivo e verifique se contém: `76561198832506980`
3. Certifique-se que não há linhas vazias extras

#### **2. Atualizar Lista no Gerenciador**
1. Abra o gerenciador
2. Vá para aba **"👥 Jogadores"**
3. A lista deve mostrar automaticamente
4. Se não aparecer, reinicie o gerenciador

#### **3. Verificar Caminhos**
Execute o teste de caminhos:
```bash
python testar_caminhos.py
```

---

## ❌ Problema 3: "Monitoramento de jogador não funcionou"

### 🔍 **Diagnóstico:**
O sistema não detecta jogadores online mesmo estando conectado.

### ✅ **Soluções:**

#### **1. Testar Detecção**
Execute o teste de detecção:
```bash
python testar_deteccao.py
```

#### **2. Usar Debug no Gerenciador**
1. Abra o gerenciador
2. Vá para aba **"👥 Jogadores"**
3. Clique em **"🔍 Debug Logs"**
4. Verifique se encontra linhas de conexão

#### **3. Formato de Log Esperado**
O sistema procura por linhas como:
```
[09:16:16] User '{Lidgren 192.168.1.100:53283}' '76561198832506980', approvedUserIndex: 0, Character: 'Renee' connected as ID '0,1', Entity '323167,1'.
```

#### **4. Atualização Manual**
1. Na aba **"👥 Jogadores"**
2. Clique em **"🔄 Atualizar Lista"**
3. Aguarde alguns segundos

---

## 🛠️ **Soluções Gerais**

### **1. Reiniciar Componentes**
```bash
# 1. Feche o gerenciador
# 2. Pare o servidor V Rising
# 3. Inicie o servidor pelo gerenciador
# 4. Aguarde carregar completamente
# 5. Teste as funcionalidades
```

### **2. Verificar Permissões**
- Execute o gerenciador como **Administrador**
- Verifique se tem permissão para ler/escrever em `save-data/`

### **3. Verificar Estrutura de Pastas**
```
VRisingServer/
├── VRisingServer.exe
├── VRising_Manager/
│   └── vrising_manager.py
├── save-data/
│   ├── Saves/v4/crepusculo/
│   └── Settings/
│       ├── adminlist.txt
│       └── banlist.txt
└── logs/
    └── server_*.log
```

---

## 🧪 **Scripts de Teste Disponíveis**

### **1. Testar Caminhos**
```bash
python testar_caminhos.py
```
Verifica se todos os caminhos estão corretos.

### **2. Testar Detecção de Jogadores**
```bash
python testar_deteccao.py
```
Testa se o sistema consegue detectar jogadores nos logs.

---

## 📋 **Checklist de Verificação**

### ✅ **Antes de Usar o Gerenciador:**
- [ ] VRisingServer.exe existe na pasta pai
- [ ] Pasta `save-data` existe e tem o save "crepusculo"
- [ ] Arquivo `adminlist.txt` existe e tem seu Steam ID
- [ ] Python e dependências instaladas
- [ ] Executando da pasta `VRising_Manager`

### ✅ **Para Detectar Jogadores:**
- [ ] Servidor está rodando pelo gerenciador
- [ ] Jogador está conectado no jogo
- [ ] Pasta `logs` existe e tem arquivos `.log`
- [ ] Aguardar 30 segundos para atualização automática

### ✅ **Para Ver Admins:**
- [ ] Arquivo `save-data/Settings/adminlist.txt` existe
- [ ] Contém Steam ID correto (17 dígitos)
- [ ] Sem linhas vazias extras
- [ ] Gerenciador tem permissão de leitura

---

## 🆘 **Se Nada Funcionar**

### **1. Reset Completo**
```bash
# 1. Feche tudo
# 2. Copie save-data para backup
# 3. Delete pasta VRising_Manager
# 4. Extraia novamente os arquivos
# 5. Execute install.bat
# 6. Configure novamente
```

### **2. Executar Manualmente**
```bash
# Teste se funciona executando diretamente:
cd VRising_Manager
python vrising_manager.py
```

### **3. Verificar Logs de Erro**
- Verifique mensagens de erro no console
- Use o botão "Debug Logs" no gerenciador
- Execute os scripts de teste

---

## 💡 **Dicas Importantes**

1. **Sempre reinicie o servidor** após mudanças em admins
2. **Aguarde 30 segundos** para detecção automática de jogadores
3. **Use Steam ID de 17 dígitos** (ex: 76561198832506980)
4. **Execute como administrador** se houver problemas de permissão
5. **Mantenha backups** dos arquivos importantes

---

**Se os problemas persistirem, execute os scripts de teste e verifique as mensagens de erro específicas.** 🔧
