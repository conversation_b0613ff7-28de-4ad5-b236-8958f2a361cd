#!/usr/bin/env python3
"""
Script para testar se os caminhos estão corretos
"""

import os

print("🔍 TESTE DE CAMINHOS - V Rising Manager")
print("=" * 50)

# Verificar pasta atual
print(f"📁 Pasta atual: {os.getcwd()}")
print()

# Testar caminhos relativos
paths_to_test = [
    "../save-data",
    "../logs", 
    "../backups",
    "../VRisingServer.exe",
    "save-data",
    "logs",
    "backups", 
    "VRisingServer.exe"
]

for path in paths_to_test:
    exists = os.path.exists(path)
    status = "✅" if exists else "❌"
    print(f"{status} {path}")

print()
print("📋 ARQUIVOS ESPECÍFICOS:")

# Testar arquivos específicos
specific_files = [
    "../save-data/Settings/adminlist.txt",
    "../save-data/Settings/banlist.txt",
    "../save-data/Saves/v4/crepusculo",
    "save-data/Settings/adminlist.txt",
    "save-data/Settings/banlist.txt"
]

for file_path in specific_files:
    exists = os.path.exists(file_path)
    status = "✅" if exists else "❌"
    print(f"{status} {file_path}")
    
    if exists and file_path.endswith('.txt'):
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
                lines = len([line for line in content.split('\n') if line.strip()])
                print(f"    📄 Conteúdo: {lines} linha(s)")
                if content:
                    print(f"    📝 Primeira linha: {content.split()[0] if content.split() else 'vazio'}")
        except Exception as e:
            print(f"    ❌ Erro ao ler: {e}")

print()
print("🎯 RECOMENDAÇÃO:")
if os.path.exists("../save-data"):
    print("✅ Use caminhos com '../' (executando da pasta VRising_Manager)")
elif os.path.exists("save-data"):
    print("✅ Use caminhos sem '../' (executando da pasta do servidor)")
else:
    print("❌ Problema: Nenhum caminho válido encontrado!")
    print("💡 Certifique-se de que está na pasta correta")

print()
input("Pressione Enter para sair...")
