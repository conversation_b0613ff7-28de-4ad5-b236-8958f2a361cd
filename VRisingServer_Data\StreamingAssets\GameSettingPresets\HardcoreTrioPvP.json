﻿{
  "GameModeType": "PvP",
  "CastleDamageMode": "TimeRestricted",
  "CastleHeartClaimMode": "TimeRestricted",
  "CastleHeartDamageMode": "CanBeSeizedOrDestroyedByPlayers",
  "DeathContainerPermission": "Anyone",
  "RelicSpawnType": "Unique",
  "BloodBoundEquipment": false,
  "PlayerDamageMode": "Always",
  "PvPProtectionMode": "Short",
  "SiegeWeaponHealth": "High",
  "AnnounceSiegeWeaponSpawn": true,
  "ShowSiegeWeaponMapIcon": false,
  "Death_DurabilityFactorLoss": 0.0,
  "Death_DurabilityLossFactorAsResources": 0.0,
  "ClanSize": 2,
  "BatBoundItems": true,
  "BatBoundShards": true,
  "CastleHeartSafetyBoxLimit": 0,
  "DropTableModifier_General": 2,
  "DropTableModifier_Missions": 2,
  "MaterialYieldModifier_Global": 2,
  "BloodEssenceYieldModifier": 2,
  "BloodDrainModifier": 1.25,
  "CastleStatModifiers_Global": {
    "HeartLimits": {
      "Level1": {
        "ServantLimit": 1
      },
      "Level2": {
        "ServantLimit": 3
      },
      "Level3": {
        "ServantLimit": 4
      },
      "Level4": {
        "ServantLimit": 5
      },
      "Level5": {
        "ServantLimit": 5
      }
    },
    "CastleLimit": 1,
    "CastleHeartLimitType": 1
  },
  "WarEventGameSettings": {
    "WeekdayTime": {
      "StartHour": 8,
      "EndHour": 2,
      "EndMinute": 0
    },
    "WeekendTime": {
      "StartHour": 8,
      "EndHour": 2,
      "EndMinute": 0
    }
  }
}