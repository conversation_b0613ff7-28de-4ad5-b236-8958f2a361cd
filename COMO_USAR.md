# 🎮 Como Usar o V Rising Server Manager

## 📋 Pré-requisitos

1. **V Rising Dedicated Server** instalado
2. **Python 3.8+** instalado no sistema
3. Gerenciador na mesma pasta do `VRisingServer.exe`

## 🚀 Instalação Rápida

### Passo 1: Verificar Estrutura
Certifique-se de que os arquivos estão organizados assim:
```
VRisingServer/
├── VRisingServer.exe          # ← Executável do servidor
├── vrising_manager.py         # ← Gerenciador (este arquivo)
├── install.bat               # ← Script de instalação
├── run_manager.bat           # ← Script para executar
├── requirements.txt          # ← Dependências
└── save-data/               # ← Dados do servidor (criado automaticamente)
```

### Passo 2: Instalar Dependências
1. **Clique duas vezes** em `install.bat`
2. Aguarde a instalação das dependências
3. Se der erro, instale Python de https://python.org

### Passo 3: Executar o Gerenciador
1. **Clique duas vezes** em `run_manager.bat`
2. Ou execute: `python vrising_manager.py`

## 🎯 Primeiro Uso

### 1. Configurar o Servidor
1. Abra o gerenciador
2. Vá para a aba **"⚙️ Configurações"**
3. Configure:
   - **Nome do Servidor**: Como aparece na lista
   - **Nome do Save**: Nome do mundo/save
   - **Porta**: 9876 (padrão) ou outra livre
   - **Máximo de Jogadores**: Quantos podem jogar
4. Clique em **"💾 Salvar Configurações"**

### 2. Iniciar o Servidor
1. Na tela principal, clique **"▶️ Iniciar"**
2. Aguarde o servidor carregar (pode demorar alguns minutos)
3. Status mudará para **"🟢 Servidor Online"**

### 3. Monitorar o Servidor
- **Dashboard**: Mostra CPU, RAM, uptime em tempo real
- **Console**: Logs do servidor aparecem automaticamente
- **Status**: Indica se está online/offline

## 📊 Funcionalidades Principais

### 🎮 Controle do Servidor
- **▶️ Iniciar**: Liga o servidor
- **⏹️ Parar**: Desliga o servidor
- **🔄 Reiniciar**: Reinicia o servidor

### 📈 Monitoramento
- **CPU**: Uso do processador
- **RAM**: Uso de memória
- **Uptime**: Tempo online
- **Logs**: Mensagens em tempo real

### 💾 Sistema de Backup
- **Backup Automático**: Criado ao iniciar servidor
- **Backup Manual**: Clique em "📦 Criar Backup"
- **Lista de Backups**: Veja todos os backups salvos
- **Localização**: Pasta `backups/`

### ⚙️ Configurações
- **Nome do Servidor**: Personalize o nome
- **Porta**: Mude se necessário
- **Jogadores**: Limite de players
- **Saves**: Gerencie diferentes mundos

## 🔧 Solução de Problemas

### ❌ "Python não encontrado"
**Solução:**
1. Instale Python de https://python.org/downloads/
2. Durante instalação, marque **"Add Python to PATH"**
3. Reinicie o computador
4. Execute `install.bat` novamente

### ❌ "VRisingServer.exe não encontrado"
**Solução:**
1. Coloque o gerenciador na mesma pasta do servidor
2. Verifique se `VRisingServer.exe` existe
3. Baixe o V Rising Dedicated Server se necessário

### ❌ "Dependências não instaladas"
**Solução:**
1. Execute `install.bat` como administrador
2. Ou instale manualmente: `pip install customtkinter psutil`

### ❌ Servidor não inicia
**Possíveis causas:**
1. **Porta em uso**: Mude a porta nas configurações
2. **Firewall**: Libere a porta no Windows Firewall
3. **Antivírus**: Adicione exceção para VRisingServer.exe
4. **Permissões**: Execute como administrador

### ❌ Interface não abre
**Solução:**
1. Teste: `python -m tkinter` (deve abrir janela)
2. Atualize Python para versão mais recente
3. Reinstale dependências: `pip install --upgrade customtkinter`

## 📁 Arquivos Importantes

- **`vrising_config.json`**: Suas configurações salvas
- **`save-data/`**: Dados do mundo/servidor
- **`logs/`**: Logs do servidor
- **`backups/`**: Backups automáticos e manuais

## 🎯 Dicas Importantes

### 🔥 Firewall
Libere a porta do servidor no Windows Firewall:
1. Painel de Controle → Sistema e Segurança → Windows Firewall
2. Configurações Avançadas → Regras de Entrada
3. Nova Regra → Porta → TCP → Sua porta (ex: 9876)

### 🌐 Acesso Externo
Para amigos se conectarem pela internet:
1. Configure port forwarding no roteador
2. Use sua IP externo + porta configurada
3. Ou use serviços como Hamachi/Radmin VPN

### 💾 Backups Regulares
- Backups automáticos são criados ao iniciar
- Faça backups manuais antes de mudanças importantes
- Mantenha vários backups de datas diferentes

### 🔄 Reinicializações
- Reinicie o servidor periodicamente (diário/semanal)
- Ajuda a manter performance e estabilidade
- Use a função "🔄 Reiniciar" do gerenciador

## 📞 Precisa de Ajuda?

1. **Verifique os logs** na aba "📋 Logs"
2. **Consulte o README_MANAGER.md** para detalhes técnicos
3. **Teste as configurações** uma por vez
4. **Reinicie** o computador se necessário

---

**Divirta-se gerenciando seu servidor V Rising!** 🧛‍♂️
